/**
 * @file IMU_Sensor_Factory.h
 * @brief IMU传感器工厂类头文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 提供自动检测和创建不同类型IMU传感器的工厂类
 * 支持SH5001、QMI8658、MPU6500、MPU9250、SC7I22等传感器
 */

#ifndef IMU_SENSOR_FACTORY_H
#define IMU_SENSOR_FACTORY_H

#include "IMU_Sensor_Interface.h"
#include <Wire.h>

/**
 * @brief 传感器检测结果结构
 */
struct Sensor_Detection_Result {
    IMU_Type type;              ///< 检测到的传感器类型
    uint8_t i2c_address;        ///< I2C地址
    uint8_t device_id;          ///< 设备ID
    const char* name;           ///< 传感器名称
    bool detected;              ///< 是否检测到
};

/**
 * @brief 传感器配置信息结构
 */
struct Sensor_Config_Info {
    IMU_Type type;              ///< 传感器类型
    uint8_t primary_address;    ///< 主I2C地址
    uint8_t secondary_address;  ///< 备用I2C地址 (如果有)
    uint8_t device_id_reg;      ///< 设备ID寄存器地址
    uint8_t expected_id;        ///< 期望的设备ID
    const char* name;           ///< 传感器名称
};

/**
 * @brief IMU传感器工厂类
 * 
 * 提供自动检测和创建不同类型IMU传感器的功能
 * 支持多种常见的IMU传感器芯片
 */
class IMU_Sensor_Factory {
private:
    static const Sensor_Config_Info sensor_configs[];  ///< 传感器配置信息数组
    static const size_t num_sensor_configs;            ///< 传感器配置数量
    
    char error_message[128];                            ///< 错误信息缓冲区
    
    /**
     * @brief 检测指定地址的传感器
     * @param address I2C地址
     * @param config 传感器配置信息
     * @return bool 是否检测到
     */
    bool detectSensor(uint8_t address, const Sensor_Config_Info& config);
    
    /**
     * @brief 读取I2C寄存器
     * @param address I2C地址
     * @param reg 寄存器地址
     * @return uint8_t 寄存器值 (失败返回0xFF)
     */
    uint8_t readI2CRegister(uint8_t address, uint8_t reg);

public:
    /**
     * @brief 构造函数
     */
    IMU_Sensor_Factory();
    
    /**
     * @brief 析构函数
     */
    ~IMU_Sensor_Factory();
    
    /**
     * @brief 自动检测IMU传感器
     * @param sda_pin SDA引脚
     * @param scl_pin SCL引脚
     * @param i2c_freq I2C频率
     * @return Sensor_Detection_Result 检测结果
     */
    Sensor_Detection_Result autoDetect(int sda_pin = 8, int scl_pin = 9, uint32_t i2c_freq = 400000);
    
    /**
     * @brief 扫描所有支持的传感器
     * @param results 检测结果数组
     * @param max_results 最大结果数量
     * @param sda_pin SDA引脚
     * @param scl_pin SCL引脚
     * @param i2c_freq I2C频率
     * @return size_t 检测到的传感器数量
     */
    size_t scanAllSensors(Sensor_Detection_Result* results, size_t max_results,
                         int sda_pin = 8, int scl_pin = 9, uint32_t i2c_freq = 400000);
    
    /**
     * @brief 创建传感器实例
     * @param type 传感器类型
     * @param address I2C地址
     * @param sda_pin SDA引脚
     * @param scl_pin SCL引脚
     * @return IMU_Sensor_Interface* 传感器实例指针 (失败返回nullptr)
     */
    IMU_Sensor_Interface* createSensor(IMU_Type type, uint8_t address = 0, 
                                      int sda_pin = 8, int scl_pin = 9);
    
    /**
     * @brief 自动检测并创建传感器实例
     * @param sda_pin SDA引脚
     * @param scl_pin SCL引脚
     * @param i2c_freq I2C频率
     * @return IMU_Sensor_Interface* 传感器实例指针 (失败返回nullptr)
     */
    IMU_Sensor_Interface* autoCreateSensor(int sda_pin = 8, int scl_pin = 9, uint32_t i2c_freq = 400000);
    
    /**
     * @brief 获取传感器类型名称
     * @param type 传感器类型
     * @return const char* 传感器名称
     */
    static const char* getSensorName(IMU_Type type);
    
    /**
     * @brief 获取传感器配置信息
     * @param type 传感器类型
     * @return const Sensor_Config_Info* 配置信息指针 (未找到返回nullptr)
     */
    static const Sensor_Config_Info* getSensorConfig(IMU_Type type);
    
    /**
     * @brief 检查传感器类型是否支持
     * @param type 传感器类型
     * @return bool 是否支持
     */
    static bool isSensorSupported(IMU_Type type);
    
    /**
     * @brief 获取最后一次错误信息
     * @return const char* 错误信息字符串
     */
    const char* getLastError() const;
    
    /**
     * @brief 获取支持的传感器列表
     * @param types 传感器类型数组
     * @param max_types 最大数组大小
     * @return size_t 支持的传感器数量
     */
    static size_t getSupportedSensors(IMU_Type* types, size_t max_types);
    
    /**
     * @brief 打印传感器扫描结果
     * @param results 检测结果数组
     * @param count 结果数量
     */
    static void printScanResults(const Sensor_Detection_Result* results, size_t count);
};

#endif // IMU_SENSOR_FACTORY_H
