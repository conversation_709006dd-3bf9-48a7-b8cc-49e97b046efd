/**
 * @file MPU9250_Adapter.h
 * @brief MPU9250传感器适配器头文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 实现MPU9250 9轴IMU传感器的接口适配
 * 支持加速度计、陀螺仪、磁力计和温度传感器
 */

#ifndef MPU9250_ADAPTER_H
#define MPU9250_ADAPTER_H

#include "IMU_Sensor_Interface.h"
#include <Wire.h>

// MPU9250寄存器地址定义
#define MPU9250_ADDR_PRIMARY    0x68    // MPU9250主I2C地址 (AD0=LOW)
#define MPU9250_ADDR_SECONDARY  0x69    // MPU9250备用I2C地址 (AD0=HIGH)

// 重要寄存器地址
#define MPU9250_WHO_AM_I        0x75    // 设备ID寄存器
#define MPU9250_EXPECTED_ID     0x71    // 期望的设备ID

#define MPU9250_PWR_MGMT_1      0x6B    // 电源管理寄存器1
#define MPU9250_PWR_MGMT_2      0x6C    // 电源管理寄存器2
#define MPU9250_CONFIG          0x1A    // 配置寄存器
#define MPU9250_GYRO_CONFIG     0x1B    // 陀螺仪配置寄存器
#define MPU9250_ACCEL_CONFIG    0x1C    // 加速度计配置寄存器
#define MPU9250_ACCEL_CONFIG2   0x1D    // 加速度计配置寄存器2

// 数据寄存器
#define MPU9250_ACCEL_XOUT_H    0x3B    // 加速度计X轴高字节
#define MPU9250_ACCEL_XOUT_L    0x3C    // 加速度计X轴低字节
#define MPU9250_ACCEL_YOUT_H    0x3D    // 加速度计Y轴高字节
#define MPU9250_ACCEL_YOUT_L    0x3E    // 加速度计Y轴低字节
#define MPU9250_ACCEL_ZOUT_H    0x3F    // 加速度计Z轴高字节
#define MPU9250_ACCEL_ZOUT_L    0x40    // 加速度计Z轴低字节

#define MPU9250_TEMP_OUT_H      0x41    // 温度高字节
#define MPU9250_TEMP_OUT_L      0x42    // 温度低字节

#define MPU9250_GYRO_XOUT_H     0x43    // 陀螺仪X轴高字节
#define MPU9250_GYRO_XOUT_L     0x44    // 陀螺仪X轴低字节
#define MPU9250_GYRO_YOUT_H     0x45    // 陀螺仪Y轴高字节
#define MPU9250_GYRO_YOUT_L     0x46    // 陀螺仪Y轴低字节
#define MPU9250_GYRO_ZOUT_H     0x47    // 陀螺仪Z轴高字节
#define MPU9250_GYRO_ZOUT_L     0x48    // 陀螺仪Z轴低字节

// 磁力计相关寄存器
#define MPU9250_I2C_MST_CTRL    0x24    // I2C主控制寄存器
#define MPU9250_USER_CTRL       0x6A    // 用户控制寄存器
#define MPU9250_INT_PIN_CFG     0x37    // 中断引脚配置寄存器

// AK8963磁力计地址和寄存器
#define AK8963_ADDRESS          0x0C    // AK8963磁力计I2C地址
#define AK8963_WHO_AM_I         0x00    // AK8963设备ID寄存器
#define AK8963_EXPECTED_ID      0x48    // AK8963期望的设备ID
#define AK8963_CNTL             0x0A    // AK8963控制寄存器
#define AK8963_XOUT_L           0x03    // AK8963 X轴数据低字节
#define AK8963_XOUT_H           0x04    // AK8963 X轴数据高字节
#define AK8963_YOUT_L           0x05    // AK8963 Y轴数据低字节
#define AK8963_YOUT_H           0x06    // AK8963 Y轴数据高字节
#define AK8963_ZOUT_L           0x07    // AK8963 Z轴数据低字节
#define AK8963_ZOUT_H           0x08    // AK8963 Z轴数据高字节
#define AK8963_ST2              0x09    // AK8963状态寄存器2

// 配置值定义
// 陀螺仪量程配置
#define MPU9250_GYRO_FS_250     0x00    // ±250 dps
#define MPU9250_GYRO_FS_500     0x08    // ±500 dps
#define MPU9250_GYRO_FS_1000    0x10    // ±1000 dps
#define MPU9250_GYRO_FS_2000    0x18    // ±2000 dps

// 加速度计量程配置
#define MPU9250_ACCEL_FS_2G     0x00    // ±2g
#define MPU9250_ACCEL_FS_4G     0x08    // ±4g
#define MPU9250_ACCEL_FS_8G     0x10    // ±8g
#define MPU9250_ACCEL_FS_16G    0x18    // ±16g

/**
 * @brief 磁力计数据结构
 */
struct Magnetometer_Data {
    float mag_x;        ///< 磁力计X轴 (μT)
    float mag_y;        ///< 磁力计Y轴 (μT)
    float mag_z;        ///< 磁力计Z轴 (μT)
    uint32_t timestamp; ///< 时间戳 (毫秒)
};

/**
 * @brief MPU9250传感器适配器类
 * 
 * 实现IMU_Sensor_Interface接口，提供MPU9250传感器的具体实现
 * 支持加速度计、陀螺仪、磁力计和温度传感器数据读取
 */
class MPU9250_Adapter : public IMU_Sensor_Interface {
private:
    uint8_t device_address;         ///< I2C设备地址
    int sda_pin;                    ///< SDA引脚
    int scl_pin;                    ///< SCL引脚
    
    float gyro_scale;               ///< 陀螺仪量程转换因子
    float accel_scale;              ///< 加速度计量程转换因子
    float mag_scale;                ///< 磁力计量程转换因子
    bool initialized;               ///< 初始化标志
    bool magnetometer_enabled;      ///< 磁力计是否启用
    
    // 校准偏移量
    float accel_offset[3];          ///< 加速度计偏移量
    float gyro_offset[3];           ///< 陀螺仪偏移量
    float mag_offset[3];            ///< 磁力计偏移量
    float mag_scale_factor[3];      ///< 磁力计缩放因子
    
    // 私有方法
    bool writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    bool readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    int16_t combineBytes(uint8_t high, uint8_t low);
    
    // 磁力计相关方法
    bool writeMagRegister(uint8_t reg, uint8_t value);
    uint8_t readMagRegister(uint8_t reg);
    bool readMagRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    
    // MPU9250特有的配置方法
    IMU_Status softReset();
    IMU_Status configureAccelerometer();
    IMU_Status configureGyroscope();
    IMU_Status configureMagnetometer();
    IMU_Status setAccelRange(uint16_t range);
    IMU_Status setGyroRange(uint16_t range);
    IMU_Status setSampleRate(uint16_t rate);
    
    // 量程转换方法
    void updateScaleFactors();
    uint8_t mapAccelRange(uint16_t range);
    uint8_t mapGyroRange(uint16_t range);

public:
    /**
     * @brief 构造函数
     * @param address I2C设备地址 (默认0x68)
     * @param sda SDA引脚 (默认8)
     * @param scl SCL引脚 (默认9)
     */
    MPU9250_Adapter(uint8_t address = MPU9250_ADDR_PRIMARY, int sda = 8, int scl = 9);
    
    /**
     * @brief 析构函数
     */
    virtual ~MPU9250_Adapter();

    // 实现IMU_Sensor_Interface接口
    virtual IMU_Status begin(const IMU_Config& config = {}) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status readRawData(IMU_RawData& raw_data) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() const override;
    virtual uint8_t getDeviceID() override;
    virtual IMU_Status reset() override;
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) override;
    virtual IMU_Status setConfig(const IMU_Config& config) override;
    virtual IMU_Status getConfig(IMU_Config& config) override;
    virtual const char* getLastError() override;
    virtual const char* getInfo() override;

    /**
     * @brief 读取磁力计数据
     * @param mag_data 磁力计数据
     * @return IMU_Status 读取状态
     */
    IMU_Status readMagnetometer(Magnetometer_Data& mag_data);

    /**
     * @brief 启用/禁用磁力计
     * @param enable 是否启用
     * @return IMU_Status 设置状态
     */
    IMU_Status enableMagnetometer(bool enable = true);

    /**
     * @brief 校准磁力计
     * @param calibration_time 校准时间 (毫秒)
     * @return IMU_Status 校准状态
     */
    IMU_Status calibrateMagnetometer(uint32_t calibration_time = 30000);

    /**
     * @brief 设置I2C地址
     * @param addr 新的I2C地址
     * @return IMU_Status 设置状态
     */
    IMU_Status setI2CAddress(uint8_t addr);

    /**
     * @brief 获取温度数据
     * @return float 温度值 (摄氏度)
     */
    float getTemperature();

    /**
     * @brief 执行自检
     * @return IMU_Status 自检状态
     */
    IMU_Status selfTest();

    /**
     * @brief 获取校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     * @param mag_offsets 磁力计偏移量 [x, y, z]
     * @param mag_scales 磁力计缩放因子 [x, y, z]
     */
    void getCalibrationOffsets(float accel_offsets[3], float gyro_offsets[3], 
                              float mag_offsets[3] = nullptr, float mag_scales[3] = nullptr);

    /**
     * @brief 设置校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     * @param mag_offsets 磁力计偏移量 [x, y, z] (可选)
     * @param mag_scales 磁力计缩放因子 [x, y, z] (可选)
     * @return IMU_Status 设置状态
     */
    IMU_Status setCalibrationOffsets(const float accel_offsets[3], const float gyro_offsets[3],
                                    const float mag_offsets[3] = nullptr, const float mag_scales[3] = nullptr);
    
    /**
     * @brief 设置数字低通滤波器
     * @param bandwidth 滤波器带宽 (Hz)
     * @return IMU_Status 设置状态
     */
    IMU_Status setDigitalLowPassFilter(uint8_t bandwidth);
    
    /**
     * @brief 检查磁力计是否可用
     * @return bool 磁力计是否可用
     */
    bool isMagnetometerAvailable();
};

#endif // MPU9250_ADAPTER_H
