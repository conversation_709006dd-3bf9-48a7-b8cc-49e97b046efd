/**
 * @file MPU6500_Adapter.h
 * @brief MPU6500传感器适配器头文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 实现MPU6500 IMU传感器的接口适配
 * 支持加速度计、陀螺仪和温度传感器
 */

#ifndef MPU6500_ADAPTER_H
#define MPU6500_ADAPTER_H

#include "IMU_Sensor_Interface.h"
#include <Wire.h>

// MPU6500寄存器地址定义
#define MPU6500_ADDR_PRIMARY    0x68    // MPU6500主I2C地址 (AD0=LOW)
#define MPU6500_ADDR_SECONDARY  0x69    // MPU6500备用I2C地址 (AD0=HIGH)

// 重要寄存器地址
#define MPU6500_WHO_AM_I        0x75    // 设备ID寄存器
#define MPU6500_EXPECTED_ID     0x70    // 期望的设备ID

#define MPU6500_PWR_MGMT_1      0x6B    // 电源管理寄存器1
#define MPU6500_PWR_MGMT_2      0x6C    // 电源管理寄存器2
#define MPU6500_CONFIG          0x1A    // 配置寄存器
#define MPU6500_GYRO_CONFIG     0x1B    // 陀螺仪配置寄存器
#define MPU6500_ACCEL_CONFIG    0x1C    // 加速度计配置寄存器
#define MPU6500_ACCEL_CONFIG2   0x1D    // 加速度计配置寄存器2

// 数据寄存器
#define MPU6500_ACCEL_XOUT_H    0x3B    // 加速度计X轴高字节
#define MPU6500_ACCEL_XOUT_L    0x3C    // 加速度计X轴低字节
#define MPU6500_ACCEL_YOUT_H    0x3D    // 加速度计Y轴高字节
#define MPU6500_ACCEL_YOUT_L    0x3E    // 加速度计Y轴低字节
#define MPU6500_ACCEL_ZOUT_H    0x3F    // 加速度计Z轴高字节
#define MPU6500_ACCEL_ZOUT_L    0x40    // 加速度计Z轴低字节

#define MPU6500_TEMP_OUT_H      0x41    // 温度高字节
#define MPU6500_TEMP_OUT_L      0x42    // 温度低字节

#define MPU6500_GYRO_XOUT_H     0x43    // 陀螺仪X轴高字节
#define MPU6500_GYRO_XOUT_L     0x44    // 陀螺仪X轴低字节
#define MPU6500_GYRO_YOUT_H     0x45    // 陀螺仪Y轴高字节
#define MPU6500_GYRO_YOUT_L     0x46    // 陀螺仪Y轴低字节
#define MPU6500_GYRO_ZOUT_H     0x47    // 陀螺仪Z轴高字节
#define MPU6500_GYRO_ZOUT_L     0x48    // 陀螺仪Z轴低字节

// 配置值定义
// 陀螺仪量程配置
#define MPU6500_GYRO_FS_250     0x00    // ±250 dps
#define MPU6500_GYRO_FS_500     0x08    // ±500 dps
#define MPU6500_GYRO_FS_1000    0x10    // ±1000 dps
#define MPU6500_GYRO_FS_2000    0x18    // ±2000 dps

// 加速度计量程配置
#define MPU6500_ACCEL_FS_2G     0x00    // ±2g
#define MPU6500_ACCEL_FS_4G     0x08    // ±4g
#define MPU6500_ACCEL_FS_8G     0x10    // ±8g
#define MPU6500_ACCEL_FS_16G    0x18    // ±16g

/**
 * @brief MPU6500传感器适配器类
 * 
 * 实现IMU_Sensor_Interface接口，提供MPU6500传感器的具体实现
 * 支持加速度计、陀螺仪和温度传感器数据读取
 */
class MPU6500_Adapter : public IMU_Sensor_Interface {
private:
    uint8_t device_address;         ///< I2C设备地址
    int sda_pin;                    ///< SDA引脚
    int scl_pin;                    ///< SCL引脚
    
    float gyro_scale;               ///< 陀螺仪量程转换因子
    float accel_scale;              ///< 加速度计量程转换因子
    bool initialized;               ///< 初始化标志
    
    // 校准偏移量
    float accel_offset[3];          ///< 加速度计偏移量
    float gyro_offset[3];           ///< 陀螺仪偏移量
    
    // 私有方法
    bool writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    bool readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    int16_t combineBytes(uint8_t high, uint8_t low);
    
    // MPU6500特有的配置方法
    IMU_Status softReset();
    IMU_Status configureAccelerometer();
    IMU_Status configureGyroscope();
    IMU_Status setAccelRange(uint16_t range);
    IMU_Status setGyroRange(uint16_t range);
    IMU_Status setSampleRate(uint16_t rate);
    
    // 量程转换方法
    void updateScaleFactors();
    uint8_t mapAccelRange(uint16_t range);
    uint8_t mapGyroRange(uint16_t range);

public:
    /**
     * @brief 构造函数
     * @param address I2C设备地址 (默认0x68)
     * @param sda SDA引脚 (默认8)
     * @param scl SCL引脚 (默认9)
     */
    MPU6500_Adapter(uint8_t address = MPU6500_ADDR_PRIMARY, int sda = 8, int scl = 9);
    
    /**
     * @brief 析构函数
     */
    virtual ~MPU6500_Adapter();

    // 实现IMU_Sensor_Interface接口
    virtual IMU_Status begin(const IMU_Config& config = {}) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status readRawData(IMU_RawData& raw_data) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() const override;
    virtual uint8_t getDeviceID() override;
    virtual IMU_Status reset() override;
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) override;
    virtual IMU_Status setConfig(const IMU_Config& config) override;
    virtual IMU_Status getConfig(IMU_Config& config) override;
    virtual const char* getLastError() override;
    virtual const char* getInfo() override;

    /**
     * @brief 设置I2C地址
     * @param addr 新的I2C地址
     * @return IMU_Status 设置状态
     */
    IMU_Status setI2CAddress(uint8_t addr);

    /**
     * @brief 获取温度数据
     * @return float 温度值 (摄氏度)
     */
    float getTemperature();

    /**
     * @brief 执行自检
     * @return IMU_Status 自检状态
     */
    IMU_Status selfTest();

    /**
     * @brief 获取校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     */
    void getCalibrationOffsets(float accel_offsets[3], float gyro_offsets[3]);

    /**
     * @brief 设置校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     * @return IMU_Status 设置状态
     */
    IMU_Status setCalibrationOffsets(const float accel_offsets[3], const float gyro_offsets[3]);
    
    /**
     * @brief 设置数字低通滤波器
     * @param bandwidth 滤波器带宽 (Hz)
     * @return IMU_Status 设置状态
     */
    IMU_Status setDigitalLowPassFilter(uint8_t bandwidth);
};

#endif // MPU6500_ADAPTER_H
