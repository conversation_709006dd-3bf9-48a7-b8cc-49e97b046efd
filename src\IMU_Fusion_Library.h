/**
 * @file IMU_Fusion_Library.h
 * @brief IMU融合库主头文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 通用的IMU数据融合库，支持多种IMU传感器
 * 提供简单易用的API接口，封装复杂的融合算法
 */

#ifndef IMU_FUSION_LIBRARY_H
#define IMU_FUSION_LIBRARY_H

// 包含所有必要的头文件
#include "IMU_Sensor_Interface.h"
#include "IMU_Fusion_Engine.h"
#include "IMU_Sensor_Factory.h"

/**
 * @brief IMU融合库主类
 * 
 * 整合传感器驱动和融合算法，提供统一的高级API
 * 支持自动传感器检测、配置和数据融合
 */
class IMU_Fusion_Library {
private:
    IMU_Sensor_Interface* sensor;       ///< IMU传感器指针
    IMU_Fusion_Engine* fusion_engine;   ///< 融合引擎指针
    
    bool sensor_owned;                  ///< 传感器是否由本类管理
    bool engine_owned;                  ///< 引擎是否由本类管理
    bool initialized;                   ///< 初始化标志
    
    uint32_t last_update_time;          ///< 上次更新时间
    uint32_t update_interval;           ///< 更新间隔 (毫秒)
    
    // 统计信息
    uint32_t total_samples;             ///< 总采样数
    uint32_t failed_samples;            ///< 失败采样数
    uint32_t start_time;                ///< 开始时间
    
    char info_buffer[256];              ///< 信息缓冲区

public:
    /**
     * @brief 默认构造函数
     */
    IMU_Fusion_Library();

    /**
     * @brief 构造函数 (指定传感器)
     * @param sensor_ptr 传感器指针
     * @param take_ownership 是否接管传感器所有权
     */
    IMU_Fusion_Library(IMU_Sensor_Interface* sensor_ptr, bool take_ownership = false);

    /**
     * @brief 析构函数
     */
    ~IMU_Fusion_Library();

    /**
     * @brief 初始化IMU融合库
     * @param sensor_config 传感器配置
     * @param fusion_config 融合算法配置
     * @return IMU_Status 初始化状态
     */
    IMU_Status begin(const IMU_Config& sensor_config = {}, 
                     const Fusion_Config& fusion_config = {});

    /**
     * @brief 自动检测并初始化IMU传感器
     * @param sda_pin SDA引脚 (默认8)
     * @param scl_pin SCL引脚 (默认9)
     * @param i2c_freq I2C频率 (默认400000)
     * @return IMU_Status 初始化状态
     */
    IMU_Status autoDetectAndInit(int sda_pin = 8, int scl_pin = 9, uint32_t i2c_freq = 400000);

    /**
     * @brief 更新IMU数据并执行融合
     * @return IMU_Status 更新状态
     */
    IMU_Status update();

    /**
     * @brief 手动更新IMU数据
     * @param accel_x 加速度计X轴 (g)
     * @param accel_y 加速度计Y轴 (g)
     * @param accel_z 加速度计Z轴 (g)
     * @param gyro_x 陀螺仪X轴 (度/秒)
     * @param gyro_y 陀螺仪Y轴 (度/秒)
     * @param gyro_z 陀螺仪Z轴 (度/秒)
     * @return IMU_Status 更新状态
     */
    IMU_Status updateManual(float accel_x, float accel_y, float accel_z,
                           float gyro_x, float gyro_y, float gyro_z);

    /**
     * @brief 获取四元数
     * @param w 四元数实部
     * @param x 四元数虚部i
     * @param y 四元数虚部j
     * @param z 四元数虚部k
     * @return IMU_Status 获取状态
     */
    IMU_Status getQuaternion(float& w, float& x, float& y, float& z);

    /**
     * @brief 获取四元数 (结构体版本)
     * @param quat 四元数数据
     * @return IMU_Status 获取状态
     */
    IMU_Status getQuaternion(Quaternion_Data& quat);

    /**
     * @brief 获取欧拉角
     * @param roll 横滚角 (度)
     * @param pitch 俯仰角 (度)
     * @param yaw 偏航角 (度)
     * @return IMU_Status 获取状态
     */
    IMU_Status getEulerAngles(float& roll, float& pitch, float& yaw);

    /**
     * @brief 获取姿态数据 (结构体版本)
     * @param attitude 姿态数据
     * @return IMU_Status 获取状态
     */
    IMU_Status getAttitude(Attitude_Data& attitude);

    /**
     * @brief 获取航向角
     * @return float 航向角 (度)
     */
    float getHeading();

    /**
     * @brief 获取原始IMU数据
     * @param data IMU数据
     * @return IMU_Status 获取状态
     */
    IMU_Status getRawData(IMU_Data& data);

    /**
     * @brief 校准传感器
     * @param calibration_time 校准时间 (毫秒)
     * @return IMU_Status 校准状态
     */
    IMU_Status calibrate(uint32_t calibration_time = 5000);

    /**
     * @brief 重置融合算法
     * @return IMU_Status 重置状态
     */
    IMU_Status resetFusion();

    /**
     * @brief 设置更新频率
     * @param frequency 更新频率 (Hz)
     * @return IMU_Status 设置状态
     */
    IMU_Status setUpdateFrequency(float frequency);

    /**
     * @brief 获取传感器类型
     * @return IMU_Type 传感器类型
     */
    IMU_Type getSensorType();

    /**
     * @brief 获取传感器信息
     * @return const char* 传感器信息字符串
     */
    const char* getSensorInfo();

    /**
     * @brief 获取库信息
     * @return const char* 库信息字符串
     */
    const char* getLibraryInfo();

    /**
     * @brief 获取统计信息
     * @param total_samples 总采样数
     * @param failed_samples 失败采样数
     * @param success_rate 成功率 (%)
     * @param uptime 运行时间 (毫秒)
     */
    void getStatistics(uint32_t& total_samples, uint32_t& failed_samples, 
                      float& success_rate, uint32_t& uptime);

    /**
     * @brief 清除统计信息
     */
    void clearStatistics();

    /**
     * @brief 检查是否已初始化
     * @return bool 初始化状态
     */
    bool isInitialized() const;

    /**
     * @brief 检查传感器连接状态
     * @return bool 连接状态
     */
    bool isSensorConnected();

    /**
     * @brief 获取最后一次错误信息
     * @return const char* 错误信息字符串
     */
    const char* getLastError();

    /**
     * @brief 设置传感器指针
     * @param sensor_ptr 传感器指针
     * @param take_ownership 是否接管所有权
     * @return IMU_Status 设置状态
     */
    IMU_Status setSensor(IMU_Sensor_Interface* sensor_ptr, bool take_ownership = false);

    /**
     * @brief 获取传感器指针
     * @return IMU_Sensor_Interface* 传感器指针
     */
    IMU_Sensor_Interface* getSensor();

    /**
     * @brief 获取融合引擎指针
     * @return IMU_Fusion_Engine* 融合引擎指针
     */
    IMU_Fusion_Engine* getFusionEngine();

    /**
     * @brief 打印四元数到串口 (兼容原有格式)
     * @param precision 小数位数 (默认4)
     */
    void printQuaternion(int precision = 4);

    /**
     * @brief 打印欧拉角到串口
     * @param precision 小数位数 (默认2)
     */
    void printEulerAngles(int precision = 2);

    /**
     * @brief 打印传感器数据到串口
     * @param precision 小数位数 (默认3)
     */
    void printSensorData(int precision = 3);
};

// 便利函数和宏定义
#define IMU_FUSION_VERSION "1.0.0"
#define IMU_FUSION_AUTHOR "ESP32-C3 IMU Fusion Project"

/**
 * @brief 创建默认的融合配置
 * @param sample_rate 采样率 (Hz)
 * @return Fusion_Config 默认配置
 */
Fusion_Config createDefaultFusionConfig(float sample_rate = 100.0f);

/**
 * @brief 创建默认的传感器配置
 * @param accel_range 加速度计量程 (g)
 * @param gyro_range 陀螺仪量程 (度/秒)
 * @param sample_rate 采样率 (Hz)
 * @return IMU_Config 默认配置
 */
IMU_Config createDefaultSensorConfig(uint16_t accel_range = 16, 
                                    uint16_t gyro_range = 2000, 
                                    uint16_t sample_rate = 125);

#endif // IMU_FUSION_LIBRARY_H
