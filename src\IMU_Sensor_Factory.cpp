/**
 * @file IMU_Sensor_Factory.cpp
 * @brief IMU传感器工厂类实现文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 */

#include "IMU_Sensor_Factory.h"
#include "SH5001_Sensor.h"
#include "QMI8658_Adapter.h"
#include "MPU6500_Adapter.h"
#include "MPU9250_Adapter.h"
#include "SC7I22_Adapter.h"

#include <string.h>

// 传感器配置信息数组
const Sensor_Config_Info IMU_Sensor_Factory::sensor_configs[] = {
    // SH5001传感器配置
    {
        .type = IMU_Type::SH5001,
        .primary_address = 0x37,
        .secondary_address = 0x36,
        .device_id_reg = 0x1F,
        .expected_id = 0xA1,
        .name = "SH5001"
    },
    // QMI8658传感器配置
    {
        .type = IMU_Type::QMI8658,
        .primary_address = 0x6B,
        .secondary_address = 0x6A,
        .device_id_reg = 0x00,
        .expected_id = 0x05,
        .name = "QMI8658"
    },
    // MPU6500传感器配置
    {
        .type = IMU_Type::MPU6500,
        .primary_address = 0x68,
        .secondary_address = 0x69,
        .device_id_reg = 0x75,
        .expected_id = 0x70,
        .name = "MPU6500"
    },
    // MPU9250传感器配置
    {
        .type = IMU_Type::MPU9250,
        .primary_address = 0x68,
        .secondary_address = 0x69,
        .device_id_reg = 0x75,
        .expected_id = 0x71,
        .name = "MPU9250"
    },
    // SC7I22传感器配置 (假设配置，需要根据实际规格调整)
    {
        .type = IMU_Type::SC7I22,
        .primary_address = 0x6A,
        .secondary_address = 0x6B,
        .device_id_reg = 0x0F,
        .expected_id = 0x22,
        .name = "SC7I22"
    }
};

const size_t IMU_Sensor_Factory::num_sensor_configs = sizeof(sensor_configs) / sizeof(sensor_configs[0]);

IMU_Sensor_Factory::IMU_Sensor_Factory() {
    strcpy(error_message, "Factory initialized");
}

IMU_Sensor_Factory::~IMU_Sensor_Factory() {
    // 析构函数
}

bool IMU_Sensor_Factory::detectSensor(uint8_t address, const Sensor_Config_Info& config) {
    uint8_t device_id = readI2CRegister(address, config.device_id_reg);
    
    if (device_id == 0xFF) {
        return false; // 通信失败
    }
    
    return (device_id == config.expected_id);
}

uint8_t IMU_Sensor_Factory::readI2CRegister(uint8_t address, uint8_t reg) {
    Wire.beginTransmission(address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0xFF; // 通信失败
    }
    
    Wire.requestFrom(address, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }
    
    return 0xFF; // 读取失败
}

Sensor_Detection_Result IMU_Sensor_Factory::autoDetect(int sda_pin, int scl_pin, uint32_t i2c_freq) {
    Sensor_Detection_Result result = {
        .type = IMU_Type::UNKNOWN,
        .i2c_address = 0,
        .device_id = 0,
        .name = "Unknown",
        .detected = false
    };
    
    // 初始化I2C
    Wire.begin(sda_pin, scl_pin);
    Wire.setClock(i2c_freq);
    
    // 遍历所有支持的传感器配置
    for (size_t i = 0; i < num_sensor_configs; i++) {
        const Sensor_Config_Info& config = sensor_configs[i];
        
        // 检测主地址
        if (detectSensor(config.primary_address, config)) {
            result.type = config.type;
            result.i2c_address = config.primary_address;
            result.device_id = config.expected_id;
            result.name = config.name;
            result.detected = true;
            
            snprintf(error_message, sizeof(error_message),
                    "Detected %s at address 0x%02X", config.name, config.primary_address);
            return result;
        }
        
        // 检测备用地址 (如果有)
        if (config.secondary_address != 0 && 
            config.secondary_address != config.primary_address) {
            if (detectSensor(config.secondary_address, config)) {
                result.type = config.type;
                result.i2c_address = config.secondary_address;
                result.device_id = config.expected_id;
                result.name = config.name;
                result.detected = true;
                
                snprintf(error_message, sizeof(error_message),
                        "Detected %s at address 0x%02X", config.name, config.secondary_address);
                return result;
            }
        }
    }
    
    strcpy(error_message, "No supported IMU sensor detected");
    return result;
}

size_t IMU_Sensor_Factory::scanAllSensors(Sensor_Detection_Result* results, size_t max_results,
                                         int sda_pin, int scl_pin, uint32_t i2c_freq) {
    if (!results || max_results == 0) {
        return 0;
    }
    
    // 初始化I2C
    Wire.begin(sda_pin, scl_pin);
    Wire.setClock(i2c_freq);
    
    size_t found_count = 0;
    
    // 遍历所有支持的传感器配置
    for (size_t i = 0; i < num_sensor_configs && found_count < max_results; i++) {
        const Sensor_Config_Info& config = sensor_configs[i];
        
        // 检测主地址
        if (detectSensor(config.primary_address, config)) {
            results[found_count] = {
                .type = config.type,
                .i2c_address = config.primary_address,
                .device_id = config.expected_id,
                .name = config.name,
                .detected = true
            };
            found_count++;
        }
        
        // 检测备用地址 (如果有且不同于主地址)
        if (found_count < max_results && 
            config.secondary_address != 0 && 
            config.secondary_address != config.primary_address) {
            if (detectSensor(config.secondary_address, config)) {
                results[found_count] = {
                    .type = config.type,
                    .i2c_address = config.secondary_address,
                    .device_id = config.expected_id,
                    .name = config.name,
                    .detected = true
                };
                found_count++;
            }
        }
    }
    
    snprintf(error_message, sizeof(error_message),
            "Scan completed, found %zu sensor(s)", found_count);
    
    return found_count;
}

IMU_Sensor_Interface* IMU_Sensor_Factory::createSensor(IMU_Type type, uint8_t address, 
                                                       int sda_pin, int scl_pin) {
    switch (type) {
        case IMU_Type::SH5001:
            if (address == 0) {
                const Sensor_Config_Info* config = getSensorConfig(type);
                address = config ? config->primary_address : 0x37;
            }
            return new SH5001_Sensor(address);
            
        case IMU_Type::QMI8658:
            if (address == 0) {
                const Sensor_Config_Info* config = getSensorConfig(type);
                address = config ? config->primary_address : 0x6B;
            }
            return new QMI8658_Adapter(address, sda_pin, scl_pin);
            
        case IMU_Type::MPU6500:
            if (address == 0) {
                const Sensor_Config_Info* config = getSensorConfig(type);
                address = config ? config->primary_address : 0x68;
            }
            return new MPU6500_Adapter(address, sda_pin, scl_pin);

        case IMU_Type::MPU9250:
            if (address == 0) {
                const Sensor_Config_Info* config = getSensorConfig(type);
                address = config ? config->primary_address : 0x68;
            }
            return new MPU9250_Adapter(address, sda_pin, scl_pin);

        case IMU_Type::SC7I22:
            if (address == 0) {
                const Sensor_Config_Info* config = getSensorConfig(type);
                address = config ? config->primary_address : 0x6A;
            }
            return new SC7I22_Adapter(address, sda_pin, scl_pin);
            
        default:
            snprintf(error_message, sizeof(error_message),
                    "Unsupported sensor type: %d", (int)type);
            return nullptr;
    }
}

IMU_Sensor_Interface* IMU_Sensor_Factory::autoCreateSensor(int sda_pin, int scl_pin, uint32_t i2c_freq) {
    Sensor_Detection_Result result = autoDetect(sda_pin, scl_pin, i2c_freq);
    
    if (!result.detected) {
        strcpy(error_message, "No sensor detected for auto-creation");
        return nullptr;
    }
    
    return createSensor(result.type, result.i2c_address, sda_pin, scl_pin);
}

const char* IMU_Sensor_Factory::getSensorName(IMU_Type type) {
    for (size_t i = 0; i < num_sensor_configs; i++) {
        if (sensor_configs[i].type == type) {
            return sensor_configs[i].name;
        }
    }
    return "Unknown";
}

const Sensor_Config_Info* IMU_Sensor_Factory::getSensorConfig(IMU_Type type) {
    for (size_t i = 0; i < num_sensor_configs; i++) {
        if (sensor_configs[i].type == type) {
            return &sensor_configs[i];
        }
    }
    return nullptr;
}

bool IMU_Sensor_Factory::isSensorSupported(IMU_Type type) {
    return getSensorConfig(type) != nullptr;
}

const char* IMU_Sensor_Factory::getLastError() const {
    return error_message;
}

size_t IMU_Sensor_Factory::getSupportedSensors(IMU_Type* types, size_t max_types) {
    if (!types || max_types == 0) {
        return 0;
    }
    
    size_t count = 0;
    for (size_t i = 0; i < num_sensor_configs && count < max_types; i++) {
        types[count++] = sensor_configs[i].type;
    }
    
    return count;
}

void IMU_Sensor_Factory::printScanResults(const Sensor_Detection_Result* results, size_t count) {
    Serial.println("========================================");
    Serial.println("IMU传感器扫描结果:");
    Serial.println("========================================");
    
    if (count == 0) {
        Serial.println("未检测到任何支持的IMU传感器");
    } else {
        for (size_t i = 0; i < count; i++) {
            const Sensor_Detection_Result& result = results[i];
            Serial.printf("%zu. %s (地址: 0x%02X, ID: 0x%02X)\n",
                         i + 1, result.name, result.i2c_address, result.device_id);
        }
    }
    
    Serial.println("========================================");
}
