/**
 * @file MPU9250_Adapter.cpp
 * @brief MPU9250传感器适配器实现文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 */

#include "MPU9250_Adapter.h"
#include <math.h>
#include <string.h>

MPU9250_Adapter::MPU9250_Adapter(uint8_t address, int sda, int scl) 
    : device_address(address)
    , sda_pin(sda)
    , scl_pin(scl)
    , gyro_scale(1.0f)
    , accel_scale(1.0f)
    , mag_scale(1.0f)
    , initialized(false)
    , magnetometer_enabled(false)
{
    // 初始化偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    memset(mag_offset, 0, sizeof(mag_offset));
    
    // 初始化磁力计缩放因子为1.0
    mag_scale_factor[0] = mag_scale_factor[1] = mag_scale_factor[2] = 1.0f;
    
    // 设置默认配置
    current_config.accel_range = 16;
    current_config.gyro_range = 2000;
    current_config.sample_rate = 1000;
    current_config.enable_filter = true;
    current_config.filter_bandwidth = 42;
    
    last_status = IMU_Status::OK;
    strcpy(error_message, "MPU9250 adapter created");
}

MPU9250_Adapter::~MPU9250_Adapter() {
    // 析构函数 - 无需特殊清理
}

IMU_Status MPU9250_Adapter::begin(const IMU_Config& config) {
    // 更新配置
    if (config.accel_range > 0) current_config.accel_range = config.accel_range;
    if (config.gyro_range > 0) current_config.gyro_range = config.gyro_range;
    if (config.sample_rate > 0) current_config.sample_rate = config.sample_rate;
    current_config.enable_filter = config.enable_filter;
    current_config.filter_bandwidth = config.filter_bandwidth;

    // 初始化I2C
    Wire.begin(sda_pin, scl_pin);
    Wire.setClock(400000);

    // 检查设备连接
    if (!isConnected()) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "MPU9250 device not found on I2C bus");
        return last_status;
    }

    // 软件复位
    IMU_Status status = softReset();
    if (status != IMU_Status::OK) {
        return status;
    }
    
    delay(100); // 等待复位完成

    // 配置传感器
    status = configureAccelerometer();
    if (status != IMU_Status::OK) {
        return status;
    }

    status = configureGyroscope();
    if (status != IMU_Status::OK) {
        return status;
    }

    // 尝试配置磁力计 (可选)
    status = configureMagnetometer();
    if (status == IMU_Status::OK) {
        magnetometer_enabled = true;
        Serial.println("MPU9250: 磁力计初始化成功");
    } else {
        magnetometer_enabled = false;
        Serial.println("MPU9250: 磁力计初始化失败，仅使用6轴IMU功能");
    }

    // 更新量程转换因子
    updateScaleFactors();

    initialized = true;
    last_status = IMU_Status::OK;
    strcpy(error_message, "MPU9250 initialized successfully");
    
    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::readData(IMU_Data& data) {
    IMU_RawData raw_data;
    IMU_Status status = readRawData(raw_data);
    
    if (status != IMU_Status::OK) {
        return status;
    }

    // 转换为物理单位
    data.accel_x = (raw_data.accel_raw[0] * accel_scale) - accel_offset[0];
    data.accel_y = (raw_data.accel_raw[1] * accel_scale) - accel_offset[1];
    data.accel_z = (raw_data.accel_raw[2] * accel_scale) - accel_offset[2];
    
    data.gyro_x = (raw_data.gyro_raw[0] * gyro_scale) - gyro_offset[0];
    data.gyro_y = (raw_data.gyro_raw[1] * gyro_scale) - gyro_offset[1];
    data.gyro_z = (raw_data.gyro_raw[2] * gyro_scale) - gyro_offset[2];
    
    // MPU9250温度转换公式: Temperature = (TEMP_OUT / 333.87) + 21.0
    data.temperature = (raw_data.temp_raw / 333.87f) + 21.0f;
    data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::readRawData(IMU_RawData& raw_data) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "MPU9250 not initialized");
        return last_status;
    }

    uint8_t buffer[14]; // 6字节加速度 + 2字节温度 + 6字节陀螺仪
    
    // 从ACCEL_XOUT_H开始连续读取14字节
    if (!readRegisters(MPU9250_ACCEL_XOUT_H, buffer, 14)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to read sensor data from MPU9250");
        return last_status;
    }

    // 解析加速度计数据
    raw_data.accel_raw[0] = combineBytes(buffer[0], buffer[1]);   // X轴
    raw_data.accel_raw[1] = combineBytes(buffer[2], buffer[3]);   // Y轴
    raw_data.accel_raw[2] = combineBytes(buffer[4], buffer[5]);   // Z轴

    // 解析温度数据
    raw_data.temp_raw = combineBytes(buffer[6], buffer[7]);

    // 解析陀螺仪数据
    raw_data.gyro_raw[0] = combineBytes(buffer[8], buffer[9]);    // X轴
    raw_data.gyro_raw[1] = combineBytes(buffer[10], buffer[11]);  // Y轴
    raw_data.gyro_raw[2] = combineBytes(buffer[12], buffer[13]);  // Z轴

    raw_data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

bool MPU9250_Adapter::isConnected() {
    uint8_t device_id = getDeviceID();
    return (device_id == MPU9250_EXPECTED_ID);
}

IMU_Type MPU9250_Adapter::getType() const {
    return IMU_Type::MPU9250;
}

uint8_t MPU9250_Adapter::getDeviceID() {
    return readRegister(MPU9250_WHO_AM_I);
}

IMU_Status MPU9250_Adapter::reset() {
    return softReset();
}

IMU_Status MPU9250_Adapter::calibrate(uint32_t calibration_time) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "MPU9250 not initialized");
        return last_status;
    }

    Serial.println("开始MPU9250校准，请保持传感器静止...");
    
    // 清零偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    
    const int num_samples = calibration_time / 10; // 每10ms采样一次
    float accel_sum[3] = {0, 0, 0};
    float gyro_sum[3] = {0, 0, 0};
    int valid_samples = 0;
    
    uint32_t start_time = millis();
    
    for (int i = 0; i < num_samples; i++) {
        IMU_Data data;
        if (readData(data) == IMU_Status::OK) {
            accel_sum[0] += data.accel_x;
            accel_sum[1] += data.accel_y;
            accel_sum[2] += data.accel_z;
            
            gyro_sum[0] += data.gyro_x;
            gyro_sum[1] += data.gyro_y;
            gyro_sum[2] += data.gyro_z;
            
            valid_samples++;
        }
        
        delay(10);
        
        // 显示进度
        if (i % (num_samples / 10) == 0) {
            Serial.printf("校准进度: %d%%\n", (i * 100) / num_samples);
        }
    }
    
    if (valid_samples < num_samples / 2) {
        last_status = IMU_Status::ERROR_CALIBRATION;
        strcpy(error_message, "Insufficient valid samples for calibration");
        return last_status;
    }
    
    // 计算偏移量
    gyro_offset[0] = gyro_sum[0] / valid_samples;
    gyro_offset[1] = gyro_sum[1] / valid_samples;
    gyro_offset[2] = gyro_sum[2] / valid_samples;
    
    // 加速度计Z轴应该接近1g，X和Y轴接近0
    accel_offset[0] = accel_sum[0] / valid_samples;
    accel_offset[1] = accel_sum[1] / valid_samples;
    accel_offset[2] = (accel_sum[2] / valid_samples) - 1.0f; // 减去重力加速度
    
    uint32_t elapsed_time = millis() - start_time;
    
    Serial.printf("MPU9250校准完成 (用时: %lu ms, 有效样本: %d)\n", elapsed_time, valid_samples);
    Serial.printf("陀螺仪偏移: X=%.3f, Y=%.3f, Z=%.3f (度/秒)\n", 
                 gyro_offset[0], gyro_offset[1], gyro_offset[2]);
    Serial.printf("加速度计偏移: X=%.3f, Y=%.3f, Z=%.3f (g)\n", 
                 accel_offset[0], accel_offset[1], accel_offset[2]);
    
    last_status = IMU_Status::OK;
    strcpy(error_message, "MPU9250 calibration completed successfully");
    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::setConfig(const IMU_Config& config) {
    current_config = config;
    
    if (initialized) {
        // 重新配置传感器
        IMU_Status status = configureAccelerometer();
        if (status != IMU_Status::OK) return status;
        
        status = configureGyroscope();
        if (status != IMU_Status::OK) return status;
        
        updateScaleFactors();
    }
    
    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::getConfig(IMU_Config& config) {
    config = current_config;
    return IMU_Status::OK;
}

const char* MPU9250_Adapter::getLastError() {
    return error_message;
}

const char* MPU9250_Adapter::getInfo() {
    static char info_buffer[512];
    snprintf(info_buffer, sizeof(info_buffer),
            "MPU9250 9轴IMU传感器\n"
            "I2C地址: 0x%02X\n"
            "设备ID: 0x%02X\n"
            "加速度计量程: ±%dg\n"
            "陀螺仪量程: ±%d度/秒\n"
            "采样率: %dHz\n"
            "数字滤波器: %s (带宽: %dHz)\n"
            "磁力计: %s",
            device_address,
            getDeviceID(),
            current_config.accel_range,
            current_config.gyro_range,
            current_config.sample_rate,
            current_config.enable_filter ? "启用" : "禁用",
            current_config.filter_bandwidth,
            magnetometer_enabled ? "可用" : "不可用");
    
    return info_buffer;
}

// 私有方法实现
bool MPU9250_Adapter::writeRegister(uint8_t reg, uint8_t value) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    Wire.write(value);
    return (Wire.endTransmission() == 0);
}

uint8_t MPU9250_Adapter::readRegister(uint8_t reg) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0xFF; // 通信失败
    }

    Wire.requestFrom(device_address, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }

    return 0xFF; // 读取失败
}

bool MPU9250_Adapter::readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return false;
    }

    Wire.requestFrom(device_address, length);
    uint8_t count = 0;
    while (Wire.available() && count < length) {
        buffer[count++] = Wire.read();
    }

    return (count == length);
}

int16_t MPU9250_Adapter::combineBytes(uint8_t high, uint8_t low) {
    return (int16_t)((high << 8) | low);
}

IMU_Status MPU9250_Adapter::softReset() {
    // 写入复位命令到PWR_MGMT_1寄存器
    if (!writeRegister(MPU9250_PWR_MGMT_1, 0x80)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to write reset command to MPU9250");
        return last_status;
    }

    delay(100); // 等待复位完成

    // 唤醒设备 (清除SLEEP位)
    if (!writeRegister(MPU9250_PWR_MGMT_1, 0x01)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to wake up MPU9250");
        return last_status;
    }

    delay(10);
    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::configureAccelerometer() {
    // 设置加速度计量程
    uint8_t accel_config = mapAccelRange(current_config.accel_range);

    if (!writeRegister(MPU9250_ACCEL_CONFIG, accel_config)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure accelerometer range");
        return last_status;
    }

    // 配置加速度计数字低通滤波器
    if (current_config.enable_filter) {
        uint8_t accel_config2 = 0x00; // 默认配置

        // 根据滤波器带宽设置
        if (current_config.filter_bandwidth <= 5) {
            accel_config2 = 0x06; // 5.05 Hz
        } else if (current_config.filter_bandwidth <= 10) {
            accel_config2 = 0x05; // 10.2 Hz
        } else if (current_config.filter_bandwidth <= 21) {
            accel_config2 = 0x04; // 21.2 Hz
        } else if (current_config.filter_bandwidth <= 44) {
            accel_config2 = 0x03; // 44.8 Hz
        } else if (current_config.filter_bandwidth <= 99) {
            accel_config2 = 0x02; // 99 Hz
        } else if (current_config.filter_bandwidth <= 218) {
            accel_config2 = 0x01; // 218.1 Hz
        } else {
            accel_config2 = 0x00; // 460 Hz
        }

        if (!writeRegister(MPU9250_ACCEL_CONFIG2, accel_config2)) {
            last_status = IMU_Status::ERROR_COMMUNICATION;
            strcpy(error_message, "Failed to configure accelerometer filter");
            return last_status;
        }
    }

    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::configureGyroscope() {
    // 设置陀螺仪量程
    uint8_t gyro_config = mapGyroRange(current_config.gyro_range);

    if (!writeRegister(MPU9250_GYRO_CONFIG, gyro_config)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure gyroscope range");
        return last_status;
    }

    // 配置陀螺仪数字低通滤波器
    if (current_config.enable_filter) {
        uint8_t config_value = 0x00; // 默认配置

        // 根据滤波器带宽设置
        if (current_config.filter_bandwidth <= 5) {
            config_value = 0x06; // 5 Hz
        } else if (current_config.filter_bandwidth <= 10) {
            config_value = 0x05; // 10 Hz
        } else if (current_config.filter_bandwidth <= 20) {
            config_value = 0x04; // 20 Hz
        } else if (current_config.filter_bandwidth <= 42) {
            config_value = 0x03; // 42 Hz
        } else if (current_config.filter_bandwidth <= 98) {
            config_value = 0x02; // 98 Hz
        } else if (current_config.filter_bandwidth <= 188) {
            config_value = 0x01; // 188 Hz
        } else {
            config_value = 0x00; // 250 Hz
        }

        if (!writeRegister(MPU9250_CONFIG, config_value)) {
            last_status = IMU_Status::ERROR_COMMUNICATION;
            strcpy(error_message, "Failed to configure gyroscope filter");
            return last_status;
        }
    }

    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::configureMagnetometer() {
    // 简化的磁力计配置 - 仅启用bypass模式以访问AK8963
    // 启用I2C bypass模式
    if (!writeRegister(MPU9250_INT_PIN_CFG, 0x02)) {
        return IMU_Status::ERROR_COMMUNICATION;
    }

    delay(10);

    // 检查AK8963是否可访问
    Wire.beginTransmission(AK8963_ADDRESS);
    if (Wire.endTransmission() != 0) {
        return IMU_Status::ERROR_COMMUNICATION;
    }

    // 读取AK8963设备ID
    Wire.beginTransmission(AK8963_ADDRESS);
    Wire.write(AK8963_WHO_AM_I);
    if (Wire.endTransmission() != 0) {
        return IMU_Status::ERROR_COMMUNICATION;
    }

    Wire.requestFrom(AK8963_ADDRESS, (uint8_t)1);
    if (!Wire.available()) {
        return IMU_Status::ERROR_COMMUNICATION;
    }

    uint8_t mag_id = Wire.read();
    if (mag_id != AK8963_EXPECTED_ID) {
        return IMU_Status::ERROR_COMMUNICATION;
    }

    // 设置AK8963为连续测量模式
    Wire.beginTransmission(AK8963_ADDRESS);
    Wire.write(AK8963_CNTL);
    Wire.write(0x16); // 连续测量模式2 (100Hz) + 16位输出
    if (Wire.endTransmission() != 0) {
        return IMU_Status::ERROR_COMMUNICATION;
    }

    delay(10);
    return IMU_Status::OK;
}

void MPU9250_Adapter::updateScaleFactors() {
    // 根据配置的量程更新转换因子
    switch (current_config.accel_range) {
        case 2:  accel_scale = 2.0f / 32768.0f; break;   // ±2g
        case 4:  accel_scale = 4.0f / 32768.0f; break;   // ±4g
        case 8:  accel_scale = 8.0f / 32768.0f; break;   // ±8g
        case 16: accel_scale = 16.0f / 32768.0f; break;  // ±16g
        default: accel_scale = 16.0f / 32768.0f; break;  // 默认±16g
    }

    switch (current_config.gyro_range) {
        case 250:  gyro_scale = 250.0f / 32768.0f; break;   // ±250 dps
        case 500:  gyro_scale = 500.0f / 32768.0f; break;   // ±500 dps
        case 1000: gyro_scale = 1000.0f / 32768.0f; break;  // ±1000 dps
        case 2000: gyro_scale = 2000.0f / 32768.0f; break;  // ±2000 dps
        default:   gyro_scale = 2000.0f / 32768.0f; break;  // 默认±2000 dps
    }

    // AK8963磁力计量程为±4912μT，16位分辨率
    mag_scale = 4912.0f / 32768.0f;
}

uint8_t MPU9250_Adapter::mapAccelRange(uint16_t range) {
    switch (range) {
        case 2:  return MPU9250_ACCEL_FS_2G;
        case 4:  return MPU9250_ACCEL_FS_4G;
        case 8:  return MPU9250_ACCEL_FS_8G;
        case 16: return MPU9250_ACCEL_FS_16G;
        default: return MPU9250_ACCEL_FS_16G; // 默认±16g
    }
}

uint8_t MPU9250_Adapter::mapGyroRange(uint16_t range) {
    switch (range) {
        case 250:  return MPU9250_GYRO_FS_250;
        case 500:  return MPU9250_GYRO_FS_500;
        case 1000: return MPU9250_GYRO_FS_1000;
        case 2000: return MPU9250_GYRO_FS_2000;
        default:   return MPU9250_GYRO_FS_2000; // 默认±2000 dps
    }
}

// 公共方法实现
IMU_Status MPU9250_Adapter::readMagnetometer(Magnetometer_Data& mag_data) {
    if (!magnetometer_enabled) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "Magnetometer not enabled");
        return last_status;
    }

    uint8_t buffer[7]; // 6字节数据 + 1字节状态

    // 读取磁力计数据
    Wire.beginTransmission(AK8963_ADDRESS);
    Wire.write(AK8963_XOUT_L);
    if (Wire.endTransmission() != 0) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to read magnetometer data");
        return last_status;
    }

    Wire.requestFrom(AK8963_ADDRESS, (uint8_t)7);
    uint8_t count = 0;
    while (Wire.available() && count < 7) {
        buffer[count++] = Wire.read();
    }

    if (count != 7) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Incomplete magnetometer data");
        return last_status;
    }

    // 检查数据有效性 (ST2寄存器)
    if (buffer[6] & 0x08) { // 磁传感器溢出
        last_status = IMU_Status::ERROR_INVALID_DATA;
        strcpy(error_message, "Magnetometer overflow");
        return last_status;
    }

    // 解析磁力计数据 (AK8963是小端序)
    int16_t mag_raw[3];
    mag_raw[0] = (int16_t)(buffer[1] << 8 | buffer[0]); // X轴
    mag_raw[1] = (int16_t)(buffer[3] << 8 | buffer[2]); // Y轴
    mag_raw[2] = (int16_t)(buffer[5] << 8 | buffer[4]); // Z轴

    // 转换为物理单位并应用校准
    mag_data.mag_x = ((mag_raw[0] * mag_scale) - mag_offset[0]) * mag_scale_factor[0];
    mag_data.mag_y = ((mag_raw[1] * mag_scale) - mag_offset[1]) * mag_scale_factor[1];
    mag_data.mag_z = ((mag_raw[2] * mag_scale) - mag_offset[2]) * mag_scale_factor[2];
    mag_data.timestamp = millis();

    return IMU_Status::OK;
}

IMU_Status MPU9250_Adapter::enableMagnetometer(bool enable) {
    if (enable) {
        IMU_Status status = configureMagnetometer();
        if (status == IMU_Status::OK) {
            magnetometer_enabled = true;
            strcpy(error_message, "Magnetometer enabled");
        }
        return status;
    } else {
        magnetometer_enabled = false;
        strcpy(error_message, "Magnetometer disabled");
        return IMU_Status::OK;
    }
}

bool MPU9250_Adapter::isMagnetometerAvailable() {
    return magnetometer_enabled;
}
