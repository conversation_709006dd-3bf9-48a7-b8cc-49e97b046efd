#ifndef _WS_QMI8658_H_
#define _WS_QMI8658_H_
#include <Arduino.h>
#include <Wire.h>
#include "IMU_Sensor_Interface.h"
#include "SensorQMI8658.hpp"

// QMI8658 I2C地址定义
#define QMI8658_L_SLAVE_ADDRESS 0x6B
#define QMI8658_H_SLAVE_ADDRESS 0x6A

/**
 * @brief QMI8658传感器包装类
 *
 * 基于现有的WS_QMI8658代码，实现IMU_Sensor_Interface接口
 * 使用SensorQMI8658库进行底层通信
 */
class QMI8658_Sensor : public IMU_Sensor_Interface {
private:
    SensorQMI8658 qmi;              ///< QMI8658传感器实例
    int sda_pin;                    ///< SDA引脚
    int scl_pin;                    ///< SCL引脚
    uint8_t i2c_addr;               ///< I2C地址
    bool initialized;               ///< 初始化标志

    // 校准偏移量
    float accel_offset[3];          ///< 加速度计偏移量
    float gyro_offset[3];           ///< 陀螺仪偏移量

public:
    /**
     * @brief 构造函数
     * @param address I2C设备地址 (默认0x6B)
     * @param sda SDA引脚 (默认8)
     * @param scl SCL引脚 (默认9)
     */
    QMI8658_Sensor(uint8_t address = QMI8658_L_SLAVE_ADDRESS, int sda = 8, int scl = 9);

    /**
     * @brief 析构函数
     */
    virtual ~QMI8658_Sensor();

    // 实现IMU_Sensor_Interface接口
    virtual IMU_Status begin(const IMU_Config& config = {}) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status readRawData(IMU_RawData& raw_data) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() const override;
    virtual uint8_t getDeviceID() override;
    virtual IMU_Status reset() override;
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) override;
    virtual IMU_Status setConfig(const IMU_Config& config) override;
    virtual IMU_Status getConfig(IMU_Config& config) override;
    virtual const char* getLastError() override;
    virtual const char* getInfo() override;
};

// 保持原有的函数接口以确保向后兼容
void QMI8658_Init();
void QMI8658_Loop();

#endif
