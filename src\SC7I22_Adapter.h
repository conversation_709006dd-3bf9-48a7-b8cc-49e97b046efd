/**
 * @file SC7I22_Adapter.h
 * @brief SC7I22传感器适配器头文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 实现SC7I22 IMU传感器的接口适配
 * 支持加速度计、陀螺仪和温度传感器
 * 
 * 注意: SC7I22的具体寄存器地址和配置需要根据实际数据手册调整
 * 这里提供一个通用的实现框架
 */

#ifndef SC7I22_ADAPTER_H
#define SC7I22_ADAPTER_H

#include "IMU_Sensor_Interface.h"
#include <Wire.h>

// SC7I22寄存器地址定义 (需要根据实际数据手册调整)
#define SC7I22_ADDR_PRIMARY     0x6A    // SC7I22主I2C地址
#define SC7I22_ADDR_SECONDARY   0x6B    // SC7I22备用I2C地址

// 重要寄存器地址 (示例，需要根据实际规格调整)
#define SC7I22_WHO_AM_I         0x0F    // 设备ID寄存器
#define SC7I22_EXPECTED_ID      0x22    // 期望的设备ID

#define SC7I22_CTRL_REG1        0x20    // 控制寄存器1
#define SC7I22_CTRL_REG2        0x21    // 控制寄存器2
#define SC7I22_CTRL_REG3        0x22    // 控制寄存器3
#define SC7I22_CTRL_REG4        0x23    // 控制寄存器4
#define SC7I22_CTRL_REG5        0x24    // 控制寄存器5

// 数据寄存器 (示例)
#define SC7I22_OUT_X_L_A        0x28    // 加速度计X轴低字节
#define SC7I22_OUT_X_H_A        0x29    // 加速度计X轴高字节
#define SC7I22_OUT_Y_L_A        0x2A    // 加速度计Y轴低字节
#define SC7I22_OUT_Y_H_A        0x2B    // 加速度计Y轴高字节
#define SC7I22_OUT_Z_L_A        0x2C    // 加速度计Z轴低字节
#define SC7I22_OUT_Z_H_A        0x2D    // 加速度计Z轴高字节

#define SC7I22_OUT_X_L_G        0x18    // 陀螺仪X轴低字节
#define SC7I22_OUT_X_H_G        0x19    // 陀螺仪X轴高字节
#define SC7I22_OUT_Y_L_G        0x1A    // 陀螺仪Y轴低字节
#define SC7I22_OUT_Y_H_G        0x1B    // 陀螺仪Y轴高字节
#define SC7I22_OUT_Z_L_G        0x1C    // 陀螺仪Z轴低字节
#define SC7I22_OUT_Z_H_G        0x1D    // 陀螺仪Z轴高字节

#define SC7I22_OUT_TEMP_L       0x15    // 温度低字节
#define SC7I22_OUT_TEMP_H       0x16    // 温度高字节

// 配置值定义 (示例，需要根据实际规格调整)
// 陀螺仪量程配置
#define SC7I22_GYRO_FS_250      0x00    // ±250 dps
#define SC7I22_GYRO_FS_500      0x10    // ±500 dps
#define SC7I22_GYRO_FS_1000     0x20    // ±1000 dps
#define SC7I22_GYRO_FS_2000     0x30    // ±2000 dps

// 加速度计量程配置
#define SC7I22_ACCEL_FS_2G      0x00    // ±2g
#define SC7I22_ACCEL_FS_4G      0x10    // ±4g
#define SC7I22_ACCEL_FS_8G      0x20    // ±8g
#define SC7I22_ACCEL_FS_16G     0x30    // ±16g

/**
 * @brief SC7I22传感器适配器类
 * 
 * 实现IMU_Sensor_Interface接口，提供SC7I22传感器的具体实现
 * 支持加速度计、陀螺仪和温度传感器数据读取
 */
class SC7I22_Adapter : public IMU_Sensor_Interface {
private:
    uint8_t device_address;         ///< I2C设备地址
    int sda_pin;                    ///< SDA引脚
    int scl_pin;                    ///< SCL引脚
    
    float gyro_scale;               ///< 陀螺仪量程转换因子
    float accel_scale;              ///< 加速度计量程转换因子
    bool initialized;               ///< 初始化标志
    
    // 校准偏移量
    float accel_offset[3];          ///< 加速度计偏移量
    float gyro_offset[3];           ///< 陀螺仪偏移量
    
    // 私有方法
    bool writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    bool readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    int16_t combineBytes(uint8_t high, uint8_t low);
    
    // SC7I22特有的配置方法
    IMU_Status softReset();
    IMU_Status configureAccelerometer();
    IMU_Status configureGyroscope();
    IMU_Status setAccelRange(uint16_t range);
    IMU_Status setGyroRange(uint16_t range);
    IMU_Status setSampleRate(uint16_t rate);
    
    // 量程转换方法
    void updateScaleFactors();
    uint8_t mapAccelRange(uint16_t range);
    uint8_t mapGyroRange(uint16_t range);

public:
    /**
     * @brief 构造函数
     * @param address I2C设备地址 (默认0x6A)
     * @param sda SDA引脚 (默认8)
     * @param scl SCL引脚 (默认9)
     */
    SC7I22_Adapter(uint8_t address = SC7I22_ADDR_PRIMARY, int sda = 8, int scl = 9);
    
    /**
     * @brief 析构函数
     */
    virtual ~SC7I22_Adapter();

    // 实现IMU_Sensor_Interface接口
    virtual IMU_Status begin(const IMU_Config& config = {}) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status readRawData(IMU_RawData& raw_data) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() const override;
    virtual uint8_t getDeviceID() override;
    virtual IMU_Status reset() override;
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) override;
    virtual IMU_Status setConfig(const IMU_Config& config) override;
    virtual IMU_Status getConfig(IMU_Config& config) override;
    virtual const char* getLastError() override;
    virtual const char* getInfo() override;

    /**
     * @brief 设置I2C地址
     * @param addr 新的I2C地址
     * @return IMU_Status 设置状态
     */
    IMU_Status setI2CAddress(uint8_t addr);

    /**
     * @brief 获取温度数据
     * @return float 温度值 (摄氏度)
     */
    float getTemperature();

    /**
     * @brief 执行自检
     * @return IMU_Status 自检状态
     */
    IMU_Status selfTest();

    /**
     * @brief 获取校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     */
    void getCalibrationOffsets(float accel_offsets[3], float gyro_offsets[3]);

    /**
     * @brief 设置校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     * @return IMU_Status 设置状态
     */
    IMU_Status setCalibrationOffsets(const float accel_offsets[3], const float gyro_offsets[3]);
    
    /**
     * @brief 设置数字低通滤波器
     * @param bandwidth 滤波器带宽 (Hz)
     * @return IMU_Status 设置状态
     */
    IMU_Status setDigitalLowPassFilter(uint8_t bandwidth);
    
    /**
     * @brief 获取传感器状态
     * @return uint8_t 状态寄存器值
     */
    uint8_t getStatus();
};

#endif // SC7I22_ADAPTER_H
