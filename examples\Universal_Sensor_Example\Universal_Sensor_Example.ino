/**
 * @file Universal_Sensor_Example.ino
 * @brief 基于现有传感器文件的通用IMU示例
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这个示例使用现有的SH5001_Sensor和WS_QMI8658文件
 * 实现多传感器支持，无需额外的适配器文件
 */

#include <IMU_Fusion_Library.h>

// 创建IMU融合库实例
IMU_Fusion_Library imu_fusion;

// 配置参数
#define SAMPLE_RATE_HZ      100     // 采样率 100Hz
#define UPDATE_INTERVAL_MS  10      // 更新间隔 10ms
#define PRINT_INTERVAL_MS   100     // 打印间隔 100ms (10Hz输出)

// I2C引脚定义 (ESP32-C3)
#define SDA_PIN 8
#define SCL_PIN 9

// 时间控制变量
unsigned long last_update_time = 0;
unsigned long last_print_time = 0;
unsigned long last_stats_time = 0;

// 统计变量
uint32_t loop_count = 0;
uint32_t successful_updates = 0;

// 传感器类型
enum DetectedSensor {
    SENSOR_NONE,
    SENSOR_SH5001,
    SENSOR_QMI8658
};

DetectedSensor detected_sensor = SENSOR_NONE;

void setup() {
    // 初始化串口
    Serial.begin(115200);
    while (!Serial) {
        delay(10);
    }
    
    Serial.println("========================================");
    Serial.println("通用IMU传感器示例");
    Serial.println("支持SH5001和QMI8658传感器");
    Serial.println("========================================");
    
    // 手动检测传感器
    detected_sensor = detectSensor();
    
    if (detected_sensor == SENSOR_NONE) {
        Serial.println("错误: 未检测到支持的传感器!");
        Serial.println("支持的传感器:");
        Serial.println("- SH5001 (地址: 0x37/0x36)");
        Serial.println("- QMI8658 (地址: 0x6B/0x6A)");
        Serial.println();
        Serial.println("请检查硬件连接和供电");
        while (1) {
            delay(1000);
        }
    }
    
    // 创建传感器和融合算法配置
    IMU_Config sensor_config = createDefaultSensorConfig(16, 2000, 125);
    Fusion_Config fusion_config = createDefaultFusionConfig(SAMPLE_RATE_HZ);
    
    // 根据检测到的传感器调整配置
    if (detected_sensor == SENSOR_QMI8658) {
        sensor_config.accel_range = 4;      // QMI8658推荐±4g
        sensor_config.gyro_range = 512;     // QMI8658推荐±512dps
        sensor_config.sample_rate = 200;    // QMI8658推荐200Hz
    }
    
    // 显示配置信息
    Serial.println("传感器配置:");
    Serial.printf("  加速度计量程: ±%dg\n", sensor_config.accel_range);
    Serial.printf("  陀螺仪量程: ±%d度/秒\n", sensor_config.gyro_range);
    Serial.printf("  采样率: %dHz\n", sensor_config.sample_rate);
    Serial.printf("  数字滤波器: %s\n", sensor_config.enable_filter ? "启用" : "禁用");
    Serial.println();
    
    // 自动检测并初始化IMU传感器
    Serial.println("正在初始化传感器...");
    IMU_Status status = imu_fusion.autoDetectAndInit(SDA_PIN, SCL_PIN, 400000);
    
    if (status != IMU_Status::OK) {
        Serial.println("错误: 传感器初始化失败!");
        Serial.printf("错误信息: %s\n", imu_fusion.getLastError());
        while (1) {
            delay(1000);
        }
    }
    
    // 初始化融合库
    Serial.println("正在初始化融合算法...");
    status = imu_fusion.begin(sensor_config, fusion_config);
    
    if (status != IMU_Status::OK) {
        Serial.println("错误: 融合库初始化失败!");
        Serial.printf("错误信息: %s\n", imu_fusion.getLastError());
        while (1) {
            delay(1000);
        }
    }
    
    // 显示传感器信息
    Serial.println("传感器信息:");
    Serial.println(imu_fusion.getSensorInfo());
    Serial.println();
    
    // 设置更新频率
    imu_fusion.setUpdateFrequency(SAMPLE_RATE_HZ);
    
    // 询问是否进行校准
    Serial.println("开始传感器校准...");
    Serial.println("校准期间请保持传感器静止...");
    
    delay(2000); // 给用户2秒时间准备
    
    status = imu_fusion.calibrate(5000); // 5秒校准
    
    if (status == IMU_Status::OK) {
        Serial.println("校准完成!");
    } else {
        Serial.println("校准失败，使用默认参数");
    }
    
    Serial.println("========================================");
    Serial.println("系统准备就绪，开始数据采集...");
    Serial.println("输出格式: w,x,y,z (四元数)");
    Serial.println("========================================");
    
    last_update_time = millis();
    last_print_time = millis();
    last_stats_time = millis();
}

void loop() {
    unsigned long current_time = millis();
    loop_count++;
    
    // 控制更新频率
    if (current_time - last_update_time >= UPDATE_INTERVAL_MS) {
        last_update_time = current_time;
        
        // 更新IMU数据并执行融合
        IMU_Status status = imu_fusion.update();
        
        if (status == IMU_Status::OK) {
            successful_updates++;
        } else {
            // 只在连续失败时打印错误信息，避免刷屏
            static uint32_t last_error_time = 0;
            if (current_time - last_error_time > 1000) { // 每秒最多打印一次错误
                Serial.printf("警告: 数据更新失败 - %s\n", imu_fusion.getLastError());
                last_error_time = current_time;
            }
        }
    }
    
    // 控制输出频率
    if (current_time - last_print_time >= PRINT_INTERVAL_MS) {
        last_print_time = current_time;
        
        // 输出四元数 (与原有格式兼容)
        imu_fusion.printQuaternion(4);
    }
    
    // 每10秒显示一次统计信息
    if (current_time - last_stats_time >= 10000) {
        last_stats_time = current_time;
        
        uint32_t total_samples, failed_samples, uptime;
        float success_rate;
        imu_fusion.getStatistics(total_samples, failed_samples, success_rate, uptime);
        
        Serial.println();
        Serial.println("========== 统计信息 ==========");
        Serial.printf("运行时间: %lu 秒\n", uptime / 1000);
        Serial.printf("总采样数: %lu\n", total_samples);
        Serial.printf("失败采样数: %lu\n", failed_samples);
        Serial.printf("成功率: %.1f%%\n", success_rate);
        Serial.printf("循环频率: %.1f Hz\n", loop_count * 1000.0f / uptime);
        Serial.printf("更新频率: %.1f Hz\n", successful_updates * 1000.0f / uptime);
        Serial.println("==============================");
        Serial.println();
    }
    
    // 短暂延时，避免占用过多CPU
    delay(1);
}

/**
 * @brief 手动检测传感器类型
 */
DetectedSensor detectSensor() {
    Serial.println("开始手动检测传感器...");
    
    Wire.begin(SDA_PIN, SCL_PIN);
    Wire.setClock(400000);
    
    // 检测SH5001传感器
    Serial.println("检测SH5001传感器...");
    
    // 检测主地址 0x37
    if (testI2CAddress(0x37)) {
        uint8_t device_id = readI2CRegister(0x37, 0x1F);
        if (device_id == 0xA1) {
            Serial.printf("发现SH5001传感器 (地址: 0x37, ID: 0x%02X)\n", device_id);
            return SENSOR_SH5001;
        }
    }
    
    // 检测备用地址 0x36
    if (testI2CAddress(0x36)) {
        uint8_t device_id = readI2CRegister(0x36, 0x1F);
        if (device_id == 0xA1) {
            Serial.printf("发现SH5001传感器 (地址: 0x36, ID: 0x%02X)\n", device_id);
            return SENSOR_SH5001;
        }
    }
    
    // 检测QMI8658传感器
    Serial.println("检测QMI8658传感器...");
    
    // 检测主地址 0x6B
    if (testI2CAddress(0x6B)) {
        uint8_t device_id = readI2CRegister(0x6B, 0x00);
        if (device_id == 0x05) {
            Serial.printf("发现QMI8658传感器 (地址: 0x6B, ID: 0x%02X)\n", device_id);
            return SENSOR_QMI8658;
        }
    }
    
    // 检测备用地址 0x6A
    if (testI2CAddress(0x6A)) {
        uint8_t device_id = readI2CRegister(0x6A, 0x00);
        if (device_id == 0x05) {
            Serial.printf("发现QMI8658传感器 (地址: 0x6A, ID: 0x%02X)\n", device_id);
            return SENSOR_QMI8658;
        }
    }
    
    Serial.println("未检测到任何支持的传感器");
    return SENSOR_NONE;
}

/**
 * @brief 测试I2C地址是否有设备响应
 */
bool testI2CAddress(uint8_t address) {
    Wire.beginTransmission(address);
    return (Wire.endTransmission() == 0);
}

/**
 * @brief 读取I2C寄存器
 */
uint8_t readI2CRegister(uint8_t address, uint8_t reg) {
    Wire.beginTransmission(address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0xFF; // 通信失败
    }
    
    Wire.requestFrom(address, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }
    
    return 0xFF; // 读取失败
}
