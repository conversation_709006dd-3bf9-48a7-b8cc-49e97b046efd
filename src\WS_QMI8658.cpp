#include "WS_QMI8658.h"
#include <string.h>

#define I2C_SDA       8
#define I2C_SCL       9

SensorQMI8658 QMI;

IMUdata Accel;
IMUdata Gyro;



void QMI8658_Init()
{
  Wire.begin(I2C_SDA, I2C_SCL);                
  //Using WIRE !!
  if (!QMI.begin(Wire, QMI8658_L_SLAVE_ADDRESS, I2C_SDA, I2C_SCL)) {
      Serial.println("Failed to find QMI8658 - check your wiring!");
      while (1) {
          delay(1000);
      }
  }
  Serial.printf("Device ID: %x\r\n",QMI.getChipID());    // Get chip id

  QMI.configAccelerometer(
      SensorQMI8658::ACC_RANGE_4G,      // ACC_RANGE_2G / ACC_RANGE_4G / ACC_RANGE_8G / ACC_RANGE_16G
      SensorQMI8658::ACC_ODR_1000Hz,    // ACC_ODR_1000H / ACC_ODR_500Hz / ACC_ODR_250Hz / ACC_ODR_125Hz / ACC_ODR_62_5Hz / ACC_ODR_31_25Hz / ACC_ODR_LOWPOWER_128Hz / ACC_ODR_LOWPOWER_21Hz / ACC_ODR_LOWPOWER_11Hz / ACC_ODR_LOWPOWER_3Hz
      SensorQMI8658::LPF_MODE_0);       //LPF_MODE_0 (2.66% of ODR) / LPF_MODE_1 (3.63% of ODR) / LPF_MODE_2 (5.39% of ODR) / LPF_MODE_3 (13.37% of ODR)
  QMI.configGyroscope(
      SensorQMI8658::GYR_RANGE_64DPS,   // GYR_RANGE_16DPS / GYR_RANGE_32DPS / GYR_RANGE_64DPS / GYR_RANGE_128DPS / GYR_RANGE_256DPS / GYR_RANGE_512DPS / GYR_RANGE_1024DPS
      SensorQMI8658::GYR_ODR_896_8Hz,   // GYR_ODR_7174_4Hz / GYR_ODR_3587_2Hz / GYR_ODR_1793_6Hz / GYR_ODR_896_8Hz / GYR_ODR_448_4Hz / GYR_ODR_224_2Hz / GYR_ODR_112_1Hz / GYR_ODR_56_05Hz / GYR_ODR_28_025H
      SensorQMI8658::LPF_MODE_3);       // LPF_MODE_0 (2.66% of ODR) / LPF_MODE_1 (3.63% of ODR) / LPF_MODE_2 (5.39% of ODR) / LPF_MODE_3 (13.37% of ODR)


  // In 6DOF mode (accelerometer and gyroscope are both enabled),
  // the output data rate is derived from the nature frequency of gyroscope
  QMI.enableGyroscope();
  QMI.enableAccelerometer();
  
  QMI.dumpCtrlRegister();               // Serial.printf register configuration information
  Serial.println("Read data now...");
}


void QMI8658_Loop()
{
    if (QMI.getDataReady()) {
        if (QMI.getAccelerometer(Accel.x, Accel.y, Accel.z)) {
            Serial.printf("ACCEL:  %f  %f  %f\r\n",Accel.x,Accel.y,Accel.z);
        }

        if (QMI.getGyroscope(Gyro.x, Gyro.y, Gyro.z)) {
            Serial.printf("GYRO:  %f  %f  %f\r\n",Gyro.x,Gyro.y,Gyro.z);
        }
        Serial.printf("\t\t>      %lu   %.2f ℃\n", QMI.getTimestamp(), QMI.getTemperature_C());
        Serial.println();
    }
}

// QMI8658_Sensor类实现
QMI8658_Sensor::QMI8658_Sensor(uint8_t address, int sda, int scl)
    : sda_pin(sda), scl_pin(scl), i2c_addr(address), initialized(false) {

    // 初始化偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));

    // 设置默认配置
    current_config.accel_range = 4;
    current_config.gyro_range = 512;
    current_config.sample_rate = 200;
    current_config.enable_filter = true;
    current_config.filter_bandwidth = 50;

    last_status = IMU_Status::OK;
    strcpy(error_message, "QMI8658 sensor created");
}

QMI8658_Sensor::~QMI8658_Sensor() {
    // 析构函数
}

IMU_Status QMI8658_Sensor::begin(const IMU_Config& config) {
    // 更新配置
    if (config.accel_range > 0) current_config.accel_range = config.accel_range;
    if (config.gyro_range > 0) current_config.gyro_range = config.gyro_range;
    if (config.sample_rate > 0) current_config.sample_rate = config.sample_rate;
    current_config.enable_filter = config.enable_filter;
    current_config.filter_bandwidth = config.filter_bandwidth;

    Wire.begin(sda_pin, scl_pin);

    if (!qmi.begin(Wire, i2c_addr, sda_pin, scl_pin)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "QMI8658 initialization failed");
        return last_status;
    }

    // 配置加速度计
    SensorQMI8658::AccelRange accel_range;
    switch(current_config.accel_range) {
        case 2: accel_range = SensorQMI8658::ACC_RANGE_2G; break;
        case 4: accel_range = SensorQMI8658::ACC_RANGE_4G; break;
        case 8: accel_range = SensorQMI8658::ACC_RANGE_8G; break;
        case 16: accel_range = SensorQMI8658::ACC_RANGE_16G; break;
        default: accel_range = SensorQMI8658::ACC_RANGE_4G;
    }

    // 配置陀螺仪
    SensorQMI8658::GyroRange gyro_range;
    switch(current_config.gyro_range) {
        case 16: gyro_range = SensorQMI8658::GYR_RANGE_16DPS; break;
        case 32: gyro_range = SensorQMI8658::GYR_RANGE_32DPS; break;
        case 64: gyro_range = SensorQMI8658::GYR_RANGE_64DPS; break;
        case 128: gyro_range = SensorQMI8658::GYR_RANGE_128DPS; break;
        case 256: gyro_range = SensorQMI8658::GYR_RANGE_256DPS; break;
        case 512: gyro_range = SensorQMI8658::GYR_RANGE_512DPS; break;
        case 1024: gyro_range = SensorQMI8658::GYR_RANGE_1024DPS; break;
        default: gyro_range = SensorQMI8658::GYR_RANGE_512DPS;
    }

    // 配置传感器（使用3参数版本）
    qmi.configAccelerometer(accel_range,
                           SensorQMI8658::ACC_ODR_1000Hz,
                           SensorQMI8658::LPF_MODE_0);

    qmi.configGyroscope(gyro_range,
                       SensorQMI8658::GYR_ODR_896_8Hz,
                       SensorQMI8658::LPF_MODE_3);

    qmi.enableGyroscope();
    qmi.enableAccelerometer();

    initialized = true;
    last_status = IMU_Status::OK;
    strcpy(error_message, "QMI8658 initialized successfully");

    return IMU_Status::OK;
}

IMU_Status QMI8658_Sensor::readData(IMU_Data& data) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "QMI8658 not initialized");
        return last_status;
    }

    IMUdata accel, gyro;

    if (qmi.getDataReady()) {
        if (qmi.getAccelerometer(accel.x, accel.y, accel.z) &&
            qmi.getGyroscope(gyro.x, gyro.y, gyro.z)) {

            // 应用校准偏移
            data.accel_x = accel.x - accel_offset[0];
            data.accel_y = accel.y - accel_offset[1];
            data.accel_z = accel.z - accel_offset[2];

            data.gyro_x = gyro.x - gyro_offset[0];
            data.gyro_y = gyro.y - gyro_offset[1];
            data.gyro_z = gyro.z - gyro_offset[2];

            data.temperature = qmi.getTemperature_C();
            data.timestamp = millis();

            last_status = IMU_Status::OK;
            return IMU_Status::OK;
        }
    }

    last_status = IMU_Status::ERROR_INVALID_DATA;
    strcpy(error_message, "Failed to read QMI8658 data");
    return last_status;
}

IMU_Status QMI8658_Sensor::readRawData(IMU_RawData& raw_data) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "QMI8658 not initialized");
        return last_status;
    }

    IMUdata accel, gyro;

    if (qmi.getDataReady()) {
        if (qmi.getAccelerometer(accel.x, accel.y, accel.z) &&
            qmi.getGyroscope(gyro.x, gyro.y, gyro.z)) {

            // 转换为原始数据格式 (假设16位分辨率)
            raw_data.accel_raw[0] = (int16_t)(accel.x * 32768.0f / current_config.accel_range);
            raw_data.accel_raw[1] = (int16_t)(accel.y * 32768.0f / current_config.accel_range);
            raw_data.accel_raw[2] = (int16_t)(accel.z * 32768.0f / current_config.accel_range);

            raw_data.gyro_raw[0] = (int16_t)(gyro.x * 32768.0f / current_config.gyro_range);
            raw_data.gyro_raw[1] = (int16_t)(gyro.y * 32768.0f / current_config.gyro_range);
            raw_data.gyro_raw[2] = (int16_t)(gyro.z * 32768.0f / current_config.gyro_range);

            raw_data.temp_raw = (int16_t)(qmi.getTemperature_C() * 100); // 温度*100
            raw_data.timestamp = millis();

            last_status = IMU_Status::OK;
            return IMU_Status::OK;
        }
    }

    last_status = IMU_Status::ERROR_INVALID_DATA;
    strcpy(error_message, "Failed to read QMI8658 raw data");
    return last_status;
}

bool QMI8658_Sensor::isConnected() {
    return qmi.isConnected();
}

IMU_Type QMI8658_Sensor::getType() const {
    return IMU_Type::QMI8658;
}

uint8_t QMI8658_Sensor::getDeviceID() {
    return qmi.getChipID();
}

IMU_Status QMI8658_Sensor::reset() {
    initialized = false;
    return begin(current_config);
}

IMU_Status QMI8658_Sensor::calibrate(uint32_t calibration_time) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "QMI8658 not initialized");
        return last_status;
    }

    Serial.println("开始QMI8658校准，请保持传感器静止...");

    // 清零偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));

    const int num_samples = calibration_time / 10; // 每10ms采样一次
    float accel_sum[3] = {0, 0, 0};
    float gyro_sum[3] = {0, 0, 0};
    int valid_samples = 0;

    uint32_t start_time = millis();

    for (int i = 0; i < num_samples; i++) {
        IMU_Data data;
        if (readData(data) == IMU_Status::OK) {
            accel_sum[0] += data.accel_x;
            accel_sum[1] += data.accel_y;
            accel_sum[2] += data.accel_z;

            gyro_sum[0] += data.gyro_x;
            gyro_sum[1] += data.gyro_y;
            gyro_sum[2] += data.gyro_z;

            valid_samples++;
        }

        delay(10);

        // 显示进度
        if (i % (num_samples / 10) == 0) {
            Serial.printf("校准进度: %d%%\n", (i * 100) / num_samples);
        }
    }

    if (valid_samples < num_samples / 2) {
        last_status = IMU_Status::ERROR_CALIBRATION;
        strcpy(error_message, "Insufficient valid samples for calibration");
        return last_status;
    }

    // 计算偏移量
    gyro_offset[0] = gyro_sum[0] / valid_samples;
    gyro_offset[1] = gyro_sum[1] / valid_samples;
    gyro_offset[2] = gyro_sum[2] / valid_samples;

    // 加速度计Z轴应该接近1g，X和Y轴接近0
    accel_offset[0] = accel_sum[0] / valid_samples;
    accel_offset[1] = accel_sum[1] / valid_samples;
    accel_offset[2] = (accel_sum[2] / valid_samples) - 1.0f; // 减去重力加速度

    uint32_t elapsed_time = millis() - start_time;

    Serial.printf("QMI8658校准完成 (用时: %lu ms, 有效样本: %d)\n", elapsed_time, valid_samples);
    Serial.printf("陀螺仪偏移: X=%.3f, Y=%.3f, Z=%.3f (度/秒)\n",
                 gyro_offset[0], gyro_offset[1], gyro_offset[2]);
    Serial.printf("加速度计偏移: X=%.3f, Y=%.3f, Z=%.3f (g)\n",
                 accel_offset[0], accel_offset[1], accel_offset[2]);

    last_status = IMU_Status::OK;
    strcpy(error_message, "QMI8658 calibration completed successfully");
    return IMU_Status::OK;
}

IMU_Status QMI8658_Sensor::setConfig(const IMU_Config& config) {
    current_config = config;

    if (initialized) {
        // 重新配置传感器
        return begin(config);
    }

    return IMU_Status::OK;
}

IMU_Status QMI8658_Sensor::getConfig(IMU_Config& config) {
    config = current_config;
    return IMU_Status::OK;
}

const char* QMI8658_Sensor::getLastError() {
    return error_message;
}

const char* QMI8658_Sensor::getInfo() {
    static char info_buffer[256];
    snprintf(info_buffer, sizeof(info_buffer),
            "QMI8658 6轴IMU传感器\n"
            "I2C地址: 0x%02X\n"
            "设备ID: 0x%02X\n"
            "加速度计量程: ±%dg\n"
            "陀螺仪量程: ±%d度/秒\n"
            "采样率: %dHz\n"
            "数字滤波器: %s",
            i2c_addr,
            getDeviceID(),
            current_config.accel_range,
            current_config.gyro_range,
            current_config.sample_rate,
            current_config.enable_filter ? "启用" : "禁用");

    return info_buffer;
}
