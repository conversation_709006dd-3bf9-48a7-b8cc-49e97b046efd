/**
 * @file IMU_Fusion_Engine.cpp
 * @brief IMU数据融合引擎实现文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 */

#include "IMU_Fusion_Engine.h"
#include <math.h>
#include <string.h>

// 默认配置常量
#define DEFAULT_GAIN                    0.5f
#define DEFAULT_GYRO_RANGE             2000.0f
#define DEFAULT_ACCEL_REJECTION        10.0f
#define DEFAULT_MAGNETIC_REJECTION     10.0f
#define DEFAULT_SAMPLE_PERIOD          0.01f  // 100Hz
#define DEFAULT_RECOVERY_PERIOD        500    // 5秒 @ 100Hz

#define MAX_ACCEL_THRESHOLD            50.0f  // 最大合理加速度 (g)
#define MAX_GYRO_THRESHOLD             3000.0f // 最大合理角速度 (度/秒)

IMU_Fusion_Engine::IMU_Fusion_Engine() 
    : status(Fusion_Status::OK)
    , initialized(false)
    , sample_count(0)
    , last_update_time(0)
    , max_accel_magnitude(0.0f)
    , max_gyro_magnitude(0.0f)
{
    memset(&config, 0, sizeof(config));
    memset(&attitude, 0, sizeof(attitude));
    memset(&quaternion, 0, sizeof(quaternion));
    memset(error_message, 0, sizeof(error_message));
    
    // 设置默认配置
    config.convention = FusionConventionNwu;
    config.gain = DEFAULT_GAIN;
    config.gyroscope_range = DEFAULT_GYRO_RANGE;
    config.acceleration_rejection = DEFAULT_ACCEL_REJECTION;
    config.magnetic_rejection = DEFAULT_MAGNETIC_REJECTION;
    config.recovery_trigger_period = DEFAULT_RECOVERY_PERIOD;
    config.sample_period = DEFAULT_SAMPLE_PERIOD;
    config.use_magnetometer = false;
}

IMU_Fusion_Engine::~IMU_Fusion_Engine() {
    // 析构函数 - 无需特殊清理
}

Fusion_Status IMU_Fusion_Engine::begin(const Fusion_Config& new_config) {
    // 更新配置
    if (new_config.gain > 0) config.gain = new_config.gain;
    if (new_config.gyroscope_range > 0) config.gyroscope_range = new_config.gyroscope_range;
    if (new_config.acceleration_rejection > 0) config.acceleration_rejection = new_config.acceleration_rejection;
    if (new_config.magnetic_rejection > 0) config.magnetic_rejection = new_config.magnetic_rejection;
    if (new_config.recovery_trigger_period > 0) config.recovery_trigger_period = new_config.recovery_trigger_period;
    if (new_config.sample_period > 0) config.sample_period = new_config.sample_period;
    
    config.convention = new_config.convention;
    config.use_magnetometer = new_config.use_magnetometer;

    try {
        // 初始化AHRS算法
        FusionAhrsInitialise(&ahrs);
        
        // 设置AHRS算法参数
        const FusionAhrsSettings settings = {
            .convention = config.convention,
            .gain = config.gain,
            .gyroscopeRange = config.gyroscope_range,
            .accelerationRejection = config.acceleration_rejection,
            .magneticRejection = config.magnetic_rejection,
            .recoveryTriggerPeriod = config.recovery_trigger_period,
        };
        
        FusionAhrsSetSettings(&ahrs, &settings);
        
        // 重置统计信息
        sample_count = 0;
        max_accel_magnitude = 0.0f;
        max_gyro_magnitude = 0.0f;
        last_update_time = millis();
        
        initialized = true;
        status = Fusion_Status::OK;
        strcpy(error_message, "Fusion engine initialized successfully");
        
        return Fusion_Status::OK;
        
    } catch (...) {
        initialized = false;
        status = Fusion_Status::ERROR_INIT;
        strcpy(error_message, "Failed to initialize Fusion AHRS algorithm");
        return Fusion_Status::ERROR_INIT;
    }
}

Fusion_Status IMU_Fusion_Engine::update(const IMU_Data& data) {
    if (!initialized) {
        status = Fusion_Status::ERROR_INIT;
        strcpy(error_message, "Fusion engine not initialized");
        return status;
    }

    // 验证数据有效性
    if (!validateData(data)) {
        status = Fusion_Status::ERROR_INVALID_DATA;
        strcpy(error_message, "Invalid IMU data detected");
        return status;
    }

    try {
        // 准备Fusion库所需的数据格式
        const FusionVector gyroscope = {
            .axis = {data.gyro_x, data.gyro_y, data.gyro_z}
        };
        const FusionVector accelerometer = {
            .axis = {data.accel_x, data.accel_y, data.accel_z}
        };

        // 更新AHRS算法
        if (config.use_magnetometer) {
            // 如果有磁力计数据，这里可以扩展
            FusionAhrsUpdateNoMagnetometer(&ahrs, gyroscope, accelerometer, config.sample_period);
        } else {
            FusionAhrsUpdateNoMagnetometer(&ahrs, gyroscope, accelerometer, config.sample_period);
        }

        // 更新缓存的结果
        updateQuaternion();
        updateAttitude();
        
        // 更新统计信息
        updateStatistics(data);
        
        sample_count++;
        last_update_time = millis();
        status = Fusion_Status::OK;
        
        return Fusion_Status::OK;
        
    } catch (...) {
        status = Fusion_Status::ERROR_SENSOR_FAILURE;
        strcpy(error_message, "Error during fusion algorithm update");
        return status;
    }
}

Fusion_Status IMU_Fusion_Engine::update(float accel_x, float accel_y, float accel_z,
                                        float gyro_x, float gyro_y, float gyro_z) {
    IMU_Data data = {
        .accel_x = accel_x,
        .accel_y = accel_y,
        .accel_z = accel_z,
        .gyro_x = gyro_x,
        .gyro_y = gyro_y,
        .gyro_z = gyro_z,
        .temperature = 0.0f,
        .timestamp = millis()
    };
    
    return update(data);
}

Fusion_Status IMU_Fusion_Engine::getQuaternion(Quaternion_Data& quat) {
    if (!initialized) {
        status = Fusion_Status::ERROR_INIT;
        return status;
    }
    
    quat = quaternion;
    return Fusion_Status::OK;
}

Fusion_Status IMU_Fusion_Engine::getAttitude(Attitude_Data& att) {
    if (!initialized) {
        status = Fusion_Status::ERROR_INIT;
        return status;
    }
    
    att = attitude;
    return Fusion_Status::OK;
}

Fusion_Status IMU_Fusion_Engine::getEulerAngles(float& roll, float& pitch, float& yaw) {
    if (!initialized) {
        status = Fusion_Status::ERROR_INIT;
        return status;
    }
    
    roll = attitude.roll;
    pitch = attitude.pitch;
    yaw = attitude.yaw;
    
    return Fusion_Status::OK;
}

float IMU_Fusion_Engine::getHeading() {
    if (!initialized) {
        return 0.0f;
    }
    return attitude.heading;
}

Fusion_Status IMU_Fusion_Engine::reset() {
    if (!initialized) {
        return Fusion_Status::ERROR_INIT;
    }
    
    try {
        FusionAhrsInitialise(&ahrs);
        
        const FusionAhrsSettings settings = {
            .convention = config.convention,
            .gain = config.gain,
            .gyroscopeRange = config.gyroscope_range,
            .accelerationRejection = config.acceleration_rejection,
            .magneticRejection = config.magnetic_rejection,
            .recoveryTriggerPeriod = config.recovery_trigger_period,
        };
        
        FusionAhrsSetSettings(&ahrs, &settings);
        
        // 重置数据
        memset(&attitude, 0, sizeof(attitude));
        memset(&quaternion, 0, sizeof(quaternion));
        quaternion.w = 1.0f; // 单位四元数
        
        sample_count = 0;
        last_update_time = millis();
        status = Fusion_Status::OK;
        
        return Fusion_Status::OK;
        
    } catch (...) {
        status = Fusion_Status::ERROR_INIT;
        strcpy(error_message, "Failed to reset fusion algorithm");
        return status;
    }
}

void IMU_Fusion_Engine::updateQuaternion() {
    const FusionQuaternion q = FusionAhrsGetQuaternion(&ahrs);
    quaternion.w = q.element.w;
    quaternion.x = q.element.x;
    quaternion.y = q.element.y;
    quaternion.z = q.element.z;
}

void IMU_Fusion_Engine::updateAttitude() {
    const FusionEuler euler = FusionQuaternionToEuler(FusionAhrsGetQuaternion(&ahrs));
    attitude.roll = euler.angle.roll;
    attitude.pitch = euler.angle.pitch;
    attitude.yaw = euler.angle.yaw;
    attitude.heading = attitude.yaw; // 简化处理，实际可能需要磁偏角校正
}

bool IMU_Fusion_Engine::validateData(const IMU_Data& data) {
    // 检查加速度计数据
    float accel_mag = sqrt(data.accel_x * data.accel_x + 
                          data.accel_y * data.accel_y + 
                          data.accel_z * data.accel_z);
    
    if (accel_mag > MAX_ACCEL_THRESHOLD || accel_mag < 0.1f) {
        return false;
    }
    
    // 检查陀螺仪数据
    float gyro_mag = sqrt(data.gyro_x * data.gyro_x + 
                         data.gyro_y * data.gyro_y + 
                         data.gyro_z * data.gyro_z);
    
    if (gyro_mag > MAX_GYRO_THRESHOLD) {
        return false;
    }
    
    // 检查NaN和无穷大
    if (isnan(data.accel_x) || isnan(data.accel_y) || isnan(data.accel_z) ||
        isnan(data.gyro_x) || isnan(data.gyro_y) || isnan(data.gyro_z) ||
        isinf(data.accel_x) || isinf(data.accel_y) || isinf(data.accel_z) ||
        isinf(data.gyro_x) || isinf(data.gyro_y) || isinf(data.gyro_z)) {
        return false;
    }
    
    return true;
}

void IMU_Fusion_Engine::updateStatistics(const IMU_Data& data) {
    // 更新最大加速度幅值
    float accel_mag = sqrt(data.accel_x * data.accel_x + 
                          data.accel_y * data.accel_y + 
                          data.accel_z * data.accel_z);
    if (accel_mag > max_accel_magnitude) {
        max_accel_magnitude = accel_mag;
    }
    
    // 更新最大角速度幅值
    float gyro_mag = sqrt(data.gyro_x * data.gyro_x + 
                         data.gyro_y * data.gyro_y + 
                         data.gyro_z * data.gyro_z);
    if (gyro_mag > max_gyro_magnitude) {
        max_gyro_magnitude = gyro_mag;
    }
    
    // 检查警告条件
    if (accel_mag > 20.0f) {
        status = Fusion_Status::WARNING_HIGH_ACCEL;
    }
    if (gyro_mag > 1000.0f) {
        status = Fusion_Status::WARNING_HIGH_GYRO;
    }
}

// 其他方法的实现...
Fusion_Status IMU_Fusion_Engine::setConfig(const Fusion_Config& new_config) {
    config = new_config;
    if (initialized) {
        return begin(config); // 重新初始化
    }
    return Fusion_Status::OK;
}

Fusion_Status IMU_Fusion_Engine::getConfig(Fusion_Config& out_config) {
    out_config = config;
    return Fusion_Status::OK;
}

Fusion_Status IMU_Fusion_Engine::getStatus() const {
    return status;
}

uint32_t IMU_Fusion_Engine::getSampleCount() const {
    return sample_count;
}

const char* IMU_Fusion_Engine::getLastError() const {
    return error_message;
}

void IMU_Fusion_Engine::getStatistics(float& max_accel, float& max_gyro) {
    max_accel = max_accel_magnitude;
    max_gyro = max_gyro_magnitude;
}

void IMU_Fusion_Engine::clearStatistics() {
    max_accel_magnitude = 0.0f;
    max_gyro_magnitude = 0.0f;
    sample_count = 0;
}

bool IMU_Fusion_Engine::isInitialized() const {
    return initialized;
}
