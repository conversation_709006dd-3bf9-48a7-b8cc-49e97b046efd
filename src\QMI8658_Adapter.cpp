#include "QMI8658_Adapter.h"
#include <Wire.h>

QMI8658_Adapter::QMI8658_Adapter(uint8_t address, int sda, int scl) 
    : i2c_addr(address), sda_pin(sda), scl_pin(scl), initialized(false) {
    memset(error_msg, 0, sizeof(error_msg));
}

IMU_Status QMI8658_Adapter::begin(const IMU_Config& config) {
    Wire.begin(sda_pin, scl_pin);
    
    if (!qmi.begin(Wire, i2c_addr, sda_pin, scl_pin)) {
        strncpy(error_msg, "Failed to initialize QMI8658", sizeof(error_msg)-1);
        return IMU_Status::ERROR_INIT;
    }

    // 根据配置设置传感器参数
    SensorQMI8658::AccelRange accel_range;
    switch(config.accel_range) {
        case 2: accel_range = SensorQMI8658::ACC_RANGE_2G; break;
        case 4: accel_range = SensorQMI8658::ACC_RANGE_4G; break;
        case 8: accel_range = SensorQMI8658::ACC_RANGE_8G; break;
        case 16: accel_range = SensorQMI8658::ACC_RANGE_16G; break;
        default: accel_range = SensorQMI8658::ACC_RANGE_4G;
    }

    SensorQMI8658::GyroRange gyro_range;
    switch(config.gyro_range) {
        case 250: gyro_range = SensorQMI8658::GYR_RANGE_256DPS; break;
        case 500: gyro_range = SensorQMI8658::GYR_RANGE_512DPS; break;
        case 1000: gyro_range = SensorQMI8658::GYR_RANGE_1024DPS; break;
        case 2000: gyro_range = SensorQMI8658::GYR_RANGE_1024DPS; break;
        default: gyro_range = SensorQMI8658::GYR_RANGE_512DPS;
    }

    // 配置传感器（使用3参数版本）
    qmi.configAccelerometer(accel_range, 
                           SensorQMI8658::ACC_ODR_1000Hz, 
                           SensorQMI8658::LPF_MODE_0);
    
    qmi.configGyroscope(gyro_range, 
                       SensorQMI8658::GYR_ODR_896_8Hz, 
                       SensorQMI8658::LPF_MODE_3);

    qmi.enableAccelerometer();
    qmi.enableGyroscope();

    current_config = config;
    initialized = true;
    
    return IMU_Status::OK;
}

IMU_Status QMI8658_Adapter::readData(IMU_Data& data) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    if (qmi.getDataReady()) {
        IMUdata accel, gyro;
        
        if (qmi.getAccelerometer(accel.x, accel.y, accel.z) && 
            qmi.getGyroscope(gyro.x, gyro.y, gyro.z)) {
            
            data.accel_x = accel.x;
            data.accel_y = accel.y;
            data.accel_z = accel.z;
            data.gyro_x = gyro.x;
            data.gyro_y = gyro.y;
            data.gyro_z = gyro.z;
            data.temperature = qmi.getTemperature_C();
            data.timestamp = millis();
            
            return IMU_Status::OK;
        }
    }
    
    return IMU_Status::ERROR_READ;
}

// 其他方法实现...