/**
 * @file QMI8658_Adapter.h
 * @brief QMI8658传感器适配器，实现IMU_Sensor_Interface接口
 */

#ifndef QMI8658_ADAPTER_H
#define QMI8658_ADAPTER_H

#include "IMU_Sensor_Interface.h"
#include "SensorQMI8658.hpp"

class QMI8658_Adapter : public IMU_Sensor_Interface {
private:
    SensorQMI8658 qmi;
    int sda_pin;
    int scl_pin;
    uint8_t i2c_addr;
    bool initialized;
    char error_msg[64];
    IMU_Config current_config;

public:
    QMI8658_Adapter(uint8_t address = QMI8658_L_SLAVE_ADDRESS, 
                   int sda = 8, int scl = 9);
    
    // IMU_Sensor_Interface 实现
    virtual IMU_Status begin(const IMU_Config& config) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status calibrate(uint32_t calibration_time) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() override;
    virtual const char* getInfo() override;
    virtual const char* getLastError() override;
    
    // 特定于QMI8658的方法
    bool setAccelerometerConfig(SensorQMI8658::AccelRange range, 
                               SensorQMI8658::AccelODR odr, 
                               SensorQMI8658::LpfMode lpf);
    bool setGyroscopeConfig(SensorQMI8658::GyroRange range, 
                           SensorQMI8658::GyroODR odr, 
                           SensorQMI8658::LpfMode lpf);
};

#endif