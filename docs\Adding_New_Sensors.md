# 添加新传感器支持指南

本文档详细说明如何为IMU Fusion Library添加新的传感器支持。

## 概述

IMU Fusion Library采用插件架构设计，通过以下组件实现传感器的统一管理：

- **IMU_Sensor_Interface**: 抽象基类，定义统一接口
- **传感器适配器**: 具体传感器的实现类
- **IMU_Sensor_Factory**: 工厂类，负责传感器的检测和创建
- **IMU_Fusion_Library**: 主库类，整合所有功能

## 添加新传感器的完整流程

### 1. 准备工作

在开始之前，您需要：

- 传感器的完整数据手册
- 传感器的I2C地址和设备ID
- 寄存器映射表
- 数据格式和转换公式
- 配置选项（量程、采样率等）

### 2. 创建传感器适配器头文件

创建 `src/YourSensor_Adapter.h` 文件：

```cpp
/**
 * @file YourSensor_Adapter.h
 * @brief YourSensor传感器适配器头文件
 */

#ifndef YOURSENSOR_ADAPTER_H
#define YOURSENSOR_ADAPTER_H

#include "IMU_Sensor_Interface.h"
#include <Wire.h>

// 寄存器地址定义
#define YOURSENSOR_ADDR_PRIMARY     0x68    // 主I2C地址
#define YOURSENSOR_ADDR_SECONDARY   0x69    // 备用I2C地址
#define YOURSENSOR_WHO_AM_I         0x75    // 设备ID寄存器
#define YOURSENSOR_EXPECTED_ID      0xXX    // 期望的设备ID

// 数据寄存器
#define YOURSENSOR_ACCEL_XOUT_H     0x3B    // 加速度计X轴高字节
// ... 其他寄存器定义

// 配置值定义
#define YOURSENSOR_ACCEL_FS_2G      0x00    // ±2g
#define YOURSENSOR_ACCEL_FS_4G      0x08    // ±4g
// ... 其他配置值

class YourSensor_Adapter : public IMU_Sensor_Interface {
private:
    uint8_t device_address;
    int sda_pin;
    int scl_pin;
    float gyro_scale;
    float accel_scale;
    bool initialized;
    
    // 校准偏移量
    float accel_offset[3];
    float gyro_offset[3];
    
    // 私有方法
    bool writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    bool readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    int16_t combineBytes(uint8_t high, uint8_t low);
    
    // 传感器特有的配置方法
    IMU_Status softReset();
    IMU_Status configureAccelerometer();
    IMU_Status configureGyroscope();
    IMU_Status setAccelRange(uint16_t range);
    IMU_Status setGyroRange(uint16_t range);
    
    void updateScaleFactors();
    uint8_t mapAccelRange(uint16_t range);
    uint8_t mapGyroRange(uint16_t range);

public:
    YourSensor_Adapter(uint8_t address = YOURSENSOR_ADDR_PRIMARY, 
                       int sda = 8, int scl = 9);
    virtual ~YourSensor_Adapter();

    // 实现IMU_Sensor_Interface接口
    virtual IMU_Status begin(const IMU_Config& config = {}) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status readRawData(IMU_RawData& raw_data) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() const override;
    virtual uint8_t getDeviceID() override;
    virtual IMU_Status reset() override;
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) override;
    virtual IMU_Status setConfig(const IMU_Config& config) override;
    virtual IMU_Status getConfig(IMU_Config& config) override;
    virtual const char* getLastError() override;
    virtual const char* getInfo() override;
};

#endif // YOURSENSOR_ADAPTER_H
```

### 3. 实现传感器适配器

创建 `src/YourSensor_Adapter.cpp` 文件，实现所有接口方法。

#### 关键实现要点：

**构造函数和初始化**
```cpp
YourSensor_Adapter::YourSensor_Adapter(uint8_t address, int sda, int scl) 
    : device_address(address), sda_pin(sda), scl_pin(scl)
    , gyro_scale(1.0f), accel_scale(1.0f), initialized(false)
{
    // 初始化偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    
    // 设置默认配置
    current_config.accel_range = 16;
    current_config.gyro_range = 2000;
    current_config.sample_rate = 1000;
    current_config.enable_filter = true;
    current_config.filter_bandwidth = 42;
}
```

**设备检测**
```cpp
bool YourSensor_Adapter::isConnected() {
    uint8_t device_id = getDeviceID();
    return (device_id == YOURSENSOR_EXPECTED_ID);
}

uint8_t YourSensor_Adapter::getDeviceID() {
    return readRegister(YOURSENSOR_WHO_AM_I);
}
```

**数据读取**
```cpp
IMU_Status YourSensor_Adapter::readRawData(IMU_RawData& raw_data) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    uint8_t buffer[14]; // 根据传感器数据长度调整
    
    if (!readRegisters(YOURSENSOR_ACCEL_XOUT_H, buffer, 14)) {
        return IMU_Status::ERROR_COMMUNICATION;
    }

    // 解析数据（注意字节序）
    raw_data.accel_raw[0] = combineBytes(buffer[0], buffer[1]);
    raw_data.accel_raw[1] = combineBytes(buffer[2], buffer[3]);
    raw_data.accel_raw[2] = combineBytes(buffer[4], buffer[5]);
    
    // 温度数据
    raw_data.temp_raw = combineBytes(buffer[6], buffer[7]);
    
    // 陀螺仪数据
    raw_data.gyro_raw[0] = combineBytes(buffer[8], buffer[9]);
    raw_data.gyro_raw[1] = combineBytes(buffer[10], buffer[11]);
    raw_data.gyro_raw[2] = combineBytes(buffer[12], buffer[13]);
    
    raw_data.timestamp = millis();
    return IMU_Status::OK;
}
```

**数据转换**
```cpp
IMU_Status YourSensor_Adapter::readData(IMU_Data& data) {
    IMU_RawData raw_data;
    IMU_Status status = readRawData(raw_data);
    
    if (status != IMU_Status::OK) {
        return status;
    }

    // 转换为物理单位
    data.accel_x = (raw_data.accel_raw[0] * accel_scale) - accel_offset[0];
    data.accel_y = (raw_data.accel_raw[1] * accel_scale) - accel_offset[1];
    data.accel_z = (raw_data.accel_raw[2] * accel_scale) - accel_offset[2];
    
    data.gyro_x = (raw_data.gyro_raw[0] * gyro_scale) - gyro_offset[0];
    data.gyro_y = (raw_data.gyro_raw[1] * gyro_scale) - gyro_offset[1];
    data.gyro_z = (raw_data.gyro_raw[2] * gyro_scale) - gyro_offset[2];
    
    // 温度转换（根据传感器规格调整公式）
    data.temperature = (raw_data.temp_raw / 333.87f) + 21.0f;
    data.timestamp = millis();

    return IMU_Status::OK;
}
```

### 4. 更新传感器类型枚举

在 `src/IMU_Sensor_Interface.h` 中添加新的传感器类型：

```cpp
enum class IMU_Type {
    UNKNOWN = 0,
    SH5001,
    QMI8658,
    MPU6500,
    MPU9250,
    SC7I22,
    YOUR_SENSOR,    // 添加新传感器
    // ...
};
```

### 5. 更新传感器工厂

在 `src/IMU_Sensor_Factory.cpp` 中：

**添加头文件包含**
```cpp
#include "YourSensor_Adapter.h"
```

**添加传感器配置**
```cpp
const Sensor_Config_Info IMU_Sensor_Factory::sensor_configs[] = {
    // 现有配置...
    
    // 新传感器配置
    {
        .type = IMU_Type::YOUR_SENSOR,
        .primary_address = 0x68,
        .secondary_address = 0x69,
        .device_id_reg = 0x75,
        .expected_id = 0xXX,
        .name = "YourSensor"
    }
};
```

**添加创建逻辑**
```cpp
IMU_Sensor_Interface* IMU_Sensor_Factory::createSensor(IMU_Type type, uint8_t address, 
                                                       int sda_pin, int scl_pin) {
    switch (type) {
        // 现有传感器...
        
        case IMU_Type::YOUR_SENSOR:
            if (address == 0) {
                const Sensor_Config_Info* config = getSensorConfig(type);
                address = config ? config->primary_address : 0x68;
            }
            return new YourSensor_Adapter(address, sda_pin, scl_pin);
            
        // ...
    }
}
```

### 6. 测试新传感器

创建测试程序验证新传感器：

```cpp
#include <IMU_Fusion_Library.h>

void setup() {
    Serial.begin(115200);
    
    IMU_Sensor_Factory factory;
    
    // 测试传感器检测
    Sensor_Detection_Result results[10];
    size_t found = factory.scanAllSensors(results, 10);
    
    for (size_t i = 0; i < found; i++) {
        if (results[i].type == IMU_Type::YOUR_SENSOR) {
            Serial.println("发现新传感器!");
            
            // 测试传感器功能
            IMU_Sensor_Interface* sensor = factory.createSensor(results[i].type);
            if (sensor) {
                testSensorFunctionality(sensor);
                delete sensor;
            }
        }
    }
}

void testSensorFunctionality(IMU_Sensor_Interface* sensor) {
    // 测试初始化
    if (sensor->begin() != IMU_Status::OK) {
        Serial.println("初始化失败");
        return;
    }
    
    // 测试数据读取
    IMU_Data data;
    if (sensor->readData(data) == IMU_Status::OK) {
        Serial.printf("加速度: %.3f, %.3f, %.3f\n", 
                     data.accel_x, data.accel_y, data.accel_z);
        Serial.printf("陀螺仪: %.1f, %.1f, %.1f\n", 
                     data.gyro_x, data.gyro_y, data.gyro_z);
    }
    
    // 测试校准
    sensor->calibrate(3000);
    
    Serial.println("传感器测试完成");
}
```

## 最佳实践

1. **错误处理**: 提供详细的错误信息，便于调试
2. **数据验证**: 检查读取的数据是否合理
3. **配置灵活性**: 支持多种量程和采样率配置
4. **性能优化**: 最小化I2C通信次数
5. **文档完整**: 提供详细的注释和使用说明

## 常见问题

**Q: 如何确定传感器的字节序？**
A: 查看数据手册，大多数传感器使用大端序（高字节在前）

**Q: 温度转换公式如何确定？**
A: 参考数据手册中的温度计算公式，通常为线性转换

**Q: 如何处理传感器特有的功能？**
A: 在适配器类中添加公共方法，不要修改基类接口

**Q: 如何优化I2C通信性能？**
A: 使用连续读取，一次性读取所有传感器数据

## 提交贡献

完成新传感器支持后，欢迎提交Pull Request：

1. 确保代码符合项目编码规范
2. 添加完整的测试用例
3. 更新相关文档
4. 提供传感器的测试报告
