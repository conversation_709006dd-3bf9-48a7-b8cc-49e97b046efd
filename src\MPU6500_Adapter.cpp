/**
 * @file MPU6500_Adapter.cpp
 * @brief MPU6500传感器适配器实现文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 */

#include "MPU6500_Adapter.h"
#include <math.h>
#include <string.h>

MPU6500_Adapter::MPU6500_Adapter(uint8_t address, int sda, int scl) 
    : device_address(address)
    , sda_pin(sda)
    , scl_pin(scl)
    , gyro_scale(1.0f)
    , accel_scale(1.0f)
    , initialized(false)
{
    // 初始化偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    
    // 设置默认配置
    current_config.accel_range = 16;
    current_config.gyro_range = 2000;
    current_config.sample_rate = 1000;
    current_config.enable_filter = true;
    current_config.filter_bandwidth = 42;
    
    last_status = IMU_Status::OK;
    strcpy(error_message, "MPU6500 adapter created");
}

MPU6500_Adapter::~MPU6500_Adapter() {
    // 析构函数 - 无需特殊清理
}

IMU_Status MPU6500_Adapter::begin(const IMU_Config& config) {
    // 更新配置
    if (config.accel_range > 0) current_config.accel_range = config.accel_range;
    if (config.gyro_range > 0) current_config.gyro_range = config.gyro_range;
    if (config.sample_rate > 0) current_config.sample_rate = config.sample_rate;
    current_config.enable_filter = config.enable_filter;
    current_config.filter_bandwidth = config.filter_bandwidth;

    // 初始化I2C
    Wire.begin(sda_pin, scl_pin);
    Wire.setClock(400000);

    // 检查设备连接
    if (!isConnected()) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "MPU6500 device not found on I2C bus");
        return last_status;
    }

    // 软件复位
    IMU_Status status = softReset();
    if (status != IMU_Status::OK) {
        return status;
    }
    
    delay(100); // 等待复位完成

    // 配置传感器
    status = configureAccelerometer();
    if (status != IMU_Status::OK) {
        return status;
    }

    status = configureGyroscope();
    if (status != IMU_Status::OK) {
        return status;
    }

    // 更新量程转换因子
    updateScaleFactors();

    initialized = true;
    last_status = IMU_Status::OK;
    strcpy(error_message, "MPU6500 initialized successfully");
    
    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::readData(IMU_Data& data) {
    IMU_RawData raw_data;
    IMU_Status status = readRawData(raw_data);
    
    if (status != IMU_Status::OK) {
        return status;
    }

    // 转换为物理单位
    data.accel_x = (raw_data.accel_raw[0] * accel_scale) - accel_offset[0];
    data.accel_y = (raw_data.accel_raw[1] * accel_scale) - accel_offset[1];
    data.accel_z = (raw_data.accel_raw[2] * accel_scale) - accel_offset[2];
    
    data.gyro_x = (raw_data.gyro_raw[0] * gyro_scale) - gyro_offset[0];
    data.gyro_y = (raw_data.gyro_raw[1] * gyro_scale) - gyro_offset[1];
    data.gyro_z = (raw_data.gyro_raw[2] * gyro_scale) - gyro_offset[2];
    
    // MPU6500温度转换公式: Temperature = (TEMP_OUT / 333.87) + 21.0
    data.temperature = (raw_data.temp_raw / 333.87f) + 21.0f;
    data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::readRawData(IMU_RawData& raw_data) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "MPU6500 not initialized");
        return last_status;
    }

    uint8_t buffer[14]; // 6字节加速度 + 2字节温度 + 6字节陀螺仪
    
    // 从ACCEL_XOUT_H开始连续读取14字节
    if (!readRegisters(MPU6500_ACCEL_XOUT_H, buffer, 14)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to read sensor data from MPU6500");
        return last_status;
    }

    // 解析加速度计数据
    raw_data.accel_raw[0] = combineBytes(buffer[0], buffer[1]);   // X轴
    raw_data.accel_raw[1] = combineBytes(buffer[2], buffer[3]);   // Y轴
    raw_data.accel_raw[2] = combineBytes(buffer[4], buffer[5]);   // Z轴

    // 解析温度数据
    raw_data.temp_raw = combineBytes(buffer[6], buffer[7]);

    // 解析陀螺仪数据
    raw_data.gyro_raw[0] = combineBytes(buffer[8], buffer[9]);    // X轴
    raw_data.gyro_raw[1] = combineBytes(buffer[10], buffer[11]);  // Y轴
    raw_data.gyro_raw[2] = combineBytes(buffer[12], buffer[13]);  // Z轴

    raw_data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

bool MPU6500_Adapter::isConnected() {
    uint8_t device_id = getDeviceID();
    return (device_id == MPU6500_EXPECTED_ID);
}

IMU_Type MPU6500_Adapter::getType() const {
    return IMU_Type::MPU6500;
}

uint8_t MPU6500_Adapter::getDeviceID() {
    return readRegister(MPU6500_WHO_AM_I);
}

IMU_Status MPU6500_Adapter::reset() {
    return softReset();
}

IMU_Status MPU6500_Adapter::calibrate(uint32_t calibration_time) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "MPU6500 not initialized");
        return last_status;
    }

    Serial.println("开始MPU6500校准，请保持传感器静止...");
    
    // 清零偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    
    const int num_samples = calibration_time / 10; // 每10ms采样一次
    float accel_sum[3] = {0, 0, 0};
    float gyro_sum[3] = {0, 0, 0};
    int valid_samples = 0;
    
    uint32_t start_time = millis();
    
    for (int i = 0; i < num_samples; i++) {
        IMU_Data data;
        if (readData(data) == IMU_Status::OK) {
            accel_sum[0] += data.accel_x;
            accel_sum[1] += data.accel_y;
            accel_sum[2] += data.accel_z;
            
            gyro_sum[0] += data.gyro_x;
            gyro_sum[1] += data.gyro_y;
            gyro_sum[2] += data.gyro_z;
            
            valid_samples++;
        }
        
        delay(10);
        
        // 显示进度
        if (i % (num_samples / 10) == 0) {
            Serial.printf("校准进度: %d%%\n", (i * 100) / num_samples);
        }
    }
    
    if (valid_samples < num_samples / 2) {
        last_status = IMU_Status::ERROR_CALIBRATION;
        strcpy(error_message, "Insufficient valid samples for calibration");
        return last_status;
    }
    
    // 计算偏移量
    gyro_offset[0] = gyro_sum[0] / valid_samples;
    gyro_offset[1] = gyro_sum[1] / valid_samples;
    gyro_offset[2] = gyro_sum[2] / valid_samples;
    
    // 加速度计Z轴应该接近1g，X和Y轴接近0
    accel_offset[0] = accel_sum[0] / valid_samples;
    accel_offset[1] = accel_sum[1] / valid_samples;
    accel_offset[2] = (accel_sum[2] / valid_samples) - 1.0f; // 减去重力加速度
    
    uint32_t elapsed_time = millis() - start_time;
    
    Serial.printf("MPU6500校准完成 (用时: %lu ms, 有效样本: %d)\n", elapsed_time, valid_samples);
    Serial.printf("陀螺仪偏移: X=%.3f, Y=%.3f, Z=%.3f (度/秒)\n", 
                 gyro_offset[0], gyro_offset[1], gyro_offset[2]);
    Serial.printf("加速度计偏移: X=%.3f, Y=%.3f, Z=%.3f (g)\n", 
                 accel_offset[0], accel_offset[1], accel_offset[2]);
    
    last_status = IMU_Status::OK;
    strcpy(error_message, "MPU6500 calibration completed successfully");
    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::setConfig(const IMU_Config& config) {
    current_config = config;
    
    if (initialized) {
        // 重新配置传感器
        IMU_Status status = configureAccelerometer();
        if (status != IMU_Status::OK) return status;
        
        status = configureGyroscope();
        if (status != IMU_Status::OK) return status;
        
        updateScaleFactors();
    }
    
    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::getConfig(IMU_Config& config) {
    config = current_config;
    return IMU_Status::OK;
}

const char* MPU6500_Adapter::getLastError() {
    return error_message;
}

const char* MPU6500_Adapter::getInfo() {
    static char info_buffer[256];
    snprintf(info_buffer, sizeof(info_buffer),
            "MPU6500 IMU传感器\n"
            "I2C地址: 0x%02X\n"
            "设备ID: 0x%02X\n"
            "加速度计量程: ±%dg\n"
            "陀螺仪量程: ±%d度/秒\n"
            "采样率: %dHz\n"
            "数字滤波器: %s (带宽: %dHz)",
            device_address,
            getDeviceID(),
            current_config.accel_range,
            current_config.gyro_range,
            current_config.sample_rate,
            current_config.enable_filter ? "启用" : "禁用",
            current_config.filter_bandwidth);
    
    return info_buffer;
}

// 私有方法实现
bool MPU6500_Adapter::writeRegister(uint8_t reg, uint8_t value) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    Wire.write(value);
    return (Wire.endTransmission() == 0);
}

uint8_t MPU6500_Adapter::readRegister(uint8_t reg) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0xFF; // 通信失败
    }

    Wire.requestFrom(device_address, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }

    return 0xFF; // 读取失败
}

bool MPU6500_Adapter::readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return false;
    }

    Wire.requestFrom(device_address, length);
    uint8_t count = 0;
    while (Wire.available() && count < length) {
        buffer[count++] = Wire.read();
    }

    return (count == length);
}

int16_t MPU6500_Adapter::combineBytes(uint8_t high, uint8_t low) {
    return (int16_t)((high << 8) | low);
}

IMU_Status MPU6500_Adapter::softReset() {
    // 写入复位命令到PWR_MGMT_1寄存器
    if (!writeRegister(MPU6500_PWR_MGMT_1, 0x80)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to write reset command to MPU6500");
        return last_status;
    }

    delay(100); // 等待复位完成

    // 唤醒设备 (清除SLEEP位)
    if (!writeRegister(MPU6500_PWR_MGMT_1, 0x01)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to wake up MPU6500");
        return last_status;
    }

    delay(10);
    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::configureAccelerometer() {
    // 设置加速度计量程
    IMU_Status status = setAccelRange(current_config.accel_range);
    if (status != IMU_Status::OK) {
        return status;
    }

    // 配置加速度计数字低通滤波器
    if (current_config.enable_filter) {
        uint8_t accel_config2 = 0x00; // 默认配置

        // 根据滤波器带宽设置
        if (current_config.filter_bandwidth <= 5) {
            accel_config2 = 0x06; // 5.05 Hz
        } else if (current_config.filter_bandwidth <= 10) {
            accel_config2 = 0x05; // 10.2 Hz
        } else if (current_config.filter_bandwidth <= 21) {
            accel_config2 = 0x04; // 21.2 Hz
        } else if (current_config.filter_bandwidth <= 44) {
            accel_config2 = 0x03; // 44.8 Hz
        } else if (current_config.filter_bandwidth <= 99) {
            accel_config2 = 0x02; // 99 Hz
        } else if (current_config.filter_bandwidth <= 218) {
            accel_config2 = 0x01; // 218.1 Hz
        } else {
            accel_config2 = 0x00; // 460 Hz
        }

        if (!writeRegister(MPU6500_ACCEL_CONFIG2, accel_config2)) {
            last_status = IMU_Status::ERROR_COMMUNICATION;
            strcpy(error_message, "Failed to configure accelerometer filter");
            return last_status;
        }
    }

    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::configureGyroscope() {
    // 设置陀螺仪量程
    IMU_Status status = setGyroRange(current_config.gyro_range);
    if (status != IMU_Status::OK) {
        return status;
    }

    // 配置陀螺仪数字低通滤波器
    if (current_config.enable_filter) {
        uint8_t config_value = 0x00; // 默认配置

        // 根据滤波器带宽设置
        if (current_config.filter_bandwidth <= 5) {
            config_value = 0x06; // 5 Hz
        } else if (current_config.filter_bandwidth <= 10) {
            config_value = 0x05; // 10 Hz
        } else if (current_config.filter_bandwidth <= 20) {
            config_value = 0x04; // 20 Hz
        } else if (current_config.filter_bandwidth <= 42) {
            config_value = 0x03; // 42 Hz
        } else if (current_config.filter_bandwidth <= 98) {
            config_value = 0x02; // 98 Hz
        } else if (current_config.filter_bandwidth <= 188) {
            config_value = 0x01; // 188 Hz
        } else {
            config_value = 0x00; // 250 Hz
        }

        if (!writeRegister(MPU6500_CONFIG, config_value)) {
            last_status = IMU_Status::ERROR_COMMUNICATION;
            strcpy(error_message, "Failed to configure gyroscope filter");
            return last_status;
        }
    }

    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::setAccelRange(uint16_t range) {
    uint8_t config_value = mapAccelRange(range);

    if (!writeRegister(MPU6500_ACCEL_CONFIG, config_value)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to set accelerometer range");
        return last_status;
    }

    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::setGyroRange(uint16_t range) {
    uint8_t config_value = mapGyroRange(range);

    if (!writeRegister(MPU6500_GYRO_CONFIG, config_value)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to set gyroscope range");
        return last_status;
    }

    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::setSampleRate(uint16_t rate) {
    // MPU6500的采样率计算: Sample Rate = Gyroscope Output Rate / (1 + SMPLRT_DIV)
    // 陀螺仪输出率通常为1kHz (当DLPF启用时)
    uint8_t smplrt_div = 0;

    if (rate > 0 && rate <= 1000) {
        smplrt_div = (1000 / rate) - 1;
    }

    if (!writeRegister(0x19, smplrt_div)) { // SMPLRT_DIV寄存器
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to set sample rate");
        return last_status;
    }

    return IMU_Status::OK;
}

void MPU6500_Adapter::updateScaleFactors() {
    // 根据配置的量程更新转换因子
    switch (current_config.accel_range) {
        case 2:  accel_scale = 2.0f / 32768.0f; break;   // ±2g
        case 4:  accel_scale = 4.0f / 32768.0f; break;   // ±4g
        case 8:  accel_scale = 8.0f / 32768.0f; break;   // ±8g
        case 16: accel_scale = 16.0f / 32768.0f; break;  // ±16g
        default: accel_scale = 16.0f / 32768.0f; break;  // 默认±16g
    }

    switch (current_config.gyro_range) {
        case 250:  gyro_scale = 250.0f / 32768.0f; break;   // ±250 dps
        case 500:  gyro_scale = 500.0f / 32768.0f; break;   // ±500 dps
        case 1000: gyro_scale = 1000.0f / 32768.0f; break;  // ±1000 dps
        case 2000: gyro_scale = 2000.0f / 32768.0f; break;  // ±2000 dps
        default:   gyro_scale = 2000.0f / 32768.0f; break;  // 默认±2000 dps
    }
}

uint8_t MPU6500_Adapter::mapAccelRange(uint16_t range) {
    switch (range) {
        case 2:  return MPU6500_ACCEL_FS_2G;
        case 4:  return MPU6500_ACCEL_FS_4G;
        case 8:  return MPU6500_ACCEL_FS_8G;
        case 16: return MPU6500_ACCEL_FS_16G;
        default: return MPU6500_ACCEL_FS_16G; // 默认±16g
    }
}

uint8_t MPU6500_Adapter::mapGyroRange(uint16_t range) {
    switch (range) {
        case 250:  return MPU6500_GYRO_FS_250;
        case 500:  return MPU6500_GYRO_FS_500;
        case 1000: return MPU6500_GYRO_FS_1000;
        case 2000: return MPU6500_GYRO_FS_2000;
        default:   return MPU6500_GYRO_FS_2000; // 默认±2000 dps
    }
}

// 公共方法实现
IMU_Status MPU6500_Adapter::setI2CAddress(uint8_t addr) {
    device_address = addr;
    return IMU_Status::OK;
}

float MPU6500_Adapter::getTemperature() {
    uint8_t temp_h = readRegister(MPU6500_TEMP_OUT_H);
    uint8_t temp_l = readRegister(MPU6500_TEMP_OUT_L);
    int16_t temp_raw = combineBytes(temp_h, temp_l);

    // MPU6500温度转换公式: Temperature = (TEMP_OUT / 333.87) + 21.0
    return (temp_raw / 333.87f) + 21.0f;
}

IMU_Status MPU6500_Adapter::selfTest() {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "MPU6500 not initialized");
        return last_status;
    }

    // 读取设备ID验证
    uint8_t device_id = getDeviceID();
    if (device_id != MPU6500_EXPECTED_ID) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        snprintf(error_message, sizeof(error_message),
                "Device ID mismatch: expected 0x%02X, got 0x%02X",
                MPU6500_EXPECTED_ID, device_id);
        return last_status;
    }

    // 简单的数据读取测试
    IMU_Data test_data;
    IMU_Status status = readData(test_data);
    if (status != IMU_Status::OK) {
        strcpy(error_message, "Failed to read test data");
        return status;
    }

    last_status = IMU_Status::OK;
    strcpy(error_message, "MPU6500 self-test passed");
    return IMU_Status::OK;
}

void MPU6500_Adapter::getCalibrationOffsets(float accel_offsets[3], float gyro_offsets[3]) {
    memcpy(accel_offsets, accel_offset, sizeof(accel_offset));
    memcpy(gyro_offsets, gyro_offset, sizeof(gyro_offset));
}

IMU_Status MPU6500_Adapter::setCalibrationOffsets(const float accel_offsets[3], const float gyro_offsets[3]) {
    memcpy(accel_offset, accel_offsets, sizeof(accel_offset));
    memcpy(gyro_offset, gyro_offsets, sizeof(gyro_offset));

    last_status = IMU_Status::OK;
    strcpy(error_message, "Calibration offsets updated");
    return IMU_Status::OK;
}

IMU_Status MPU6500_Adapter::setDigitalLowPassFilter(uint8_t bandwidth) {
    current_config.filter_bandwidth = bandwidth;
    current_config.enable_filter = true;

    if (initialized) {
        // 重新配置滤波器
        IMU_Status status = configureGyroscope();
        if (status != IMU_Status::OK) return status;

        status = configureAccelerometer();
        return status;
    }

    return IMU_Status::OK;
}
