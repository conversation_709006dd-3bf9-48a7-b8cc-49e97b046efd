/**
 * @file SC7I22_Adapter.cpp
 * @brief SC7I22传感器适配器实现文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 注意: 这是一个基于通用IMU传感器模式的实现框架
 * 具体的寄存器地址、配置值和转换公式需要根据SC7I22的实际数据手册调整
 */

#include "SC7I22_Adapter.h"
#include <math.h>
#include <string.h>

SC7I22_Adapter::SC7I22_Adapter(uint8_t address, int sda, int scl) 
    : device_address(address)
    , sda_pin(sda)
    , scl_pin(scl)
    , gyro_scale(1.0f)
    , accel_scale(1.0f)
    , initialized(false)
{
    // 初始化偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    
    // 设置默认配置
    current_config.accel_range = 16;
    current_config.gyro_range = 2000;
    current_config.sample_rate = 1000;
    current_config.enable_filter = true;
    current_config.filter_bandwidth = 50;
    
    last_status = IMU_Status::OK;
    strcpy(error_message, "SC7I22 adapter created");
}

SC7I22_Adapter::~SC7I22_Adapter() {
    // 析构函数 - 无需特殊清理
}

IMU_Status SC7I22_Adapter::begin(const IMU_Config& config) {
    // 更新配置
    if (config.accel_range > 0) current_config.accel_range = config.accel_range;
    if (config.gyro_range > 0) current_config.gyro_range = config.gyro_range;
    if (config.sample_rate > 0) current_config.sample_rate = config.sample_rate;
    current_config.enable_filter = config.enable_filter;
    current_config.filter_bandwidth = config.filter_bandwidth;

    // 初始化I2C
    Wire.begin(sda_pin, scl_pin);
    Wire.setClock(400000);

    // 检查设备连接
    if (!isConnected()) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "SC7I22 device not found on I2C bus");
        return last_status;
    }

    // 软件复位
    IMU_Status status = softReset();
    if (status != IMU_Status::OK) {
        return status;
    }
    
    delay(100); // 等待复位完成

    // 配置传感器
    status = configureAccelerometer();
    if (status != IMU_Status::OK) {
        return status;
    }

    status = configureGyroscope();
    if (status != IMU_Status::OK) {
        return status;
    }

    // 更新量程转换因子
    updateScaleFactors();

    initialized = true;
    last_status = IMU_Status::OK;
    strcpy(error_message, "SC7I22 initialized successfully");
    
    return IMU_Status::OK;
}

IMU_Status SC7I22_Adapter::readData(IMU_Data& data) {
    IMU_RawData raw_data;
    IMU_Status status = readRawData(raw_data);
    
    if (status != IMU_Status::OK) {
        return status;
    }

    // 转换为物理单位
    data.accel_x = (raw_data.accel_raw[0] * accel_scale) - accel_offset[0];
    data.accel_y = (raw_data.accel_raw[1] * accel_scale) - accel_offset[1];
    data.accel_z = (raw_data.accel_raw[2] * accel_scale) - accel_offset[2];
    
    data.gyro_x = (raw_data.gyro_raw[0] * gyro_scale) - gyro_offset[0];
    data.gyro_y = (raw_data.gyro_raw[1] * gyro_scale) - gyro_offset[1];
    data.gyro_z = (raw_data.gyro_raw[2] * gyro_scale) - gyro_offset[2];
    
    // SC7I22温度转换公式 (需要根据实际规格调整)
    data.temperature = (raw_data.temp_raw / 256.0f) + 25.0f; // 示例转换公式
    data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

IMU_Status SC7I22_Adapter::readRawData(IMU_RawData& raw_data) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "SC7I22 not initialized");
        return last_status;
    }

    // 读取加速度计数据
    uint8_t accel_buffer[6];
    if (!readRegisters(SC7I22_OUT_X_L_A, accel_buffer, 6)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to read accelerometer data from SC7I22");
        return last_status;
    }

    // 读取陀螺仪数据
    uint8_t gyro_buffer[6];
    if (!readRegisters(SC7I22_OUT_X_L_G, gyro_buffer, 6)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to read gyroscope data from SC7I22");
        return last_status;
    }

    // 读取温度数据
    uint8_t temp_buffer[2];
    if (!readRegisters(SC7I22_OUT_TEMP_L, temp_buffer, 2)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to read temperature data from SC7I22");
        return last_status;
    }

    // 解析加速度计数据 (假设为小端序)
    raw_data.accel_raw[0] = combineBytes(accel_buffer[1], accel_buffer[0]); // X轴
    raw_data.accel_raw[1] = combineBytes(accel_buffer[3], accel_buffer[2]); // Y轴
    raw_data.accel_raw[2] = combineBytes(accel_buffer[5], accel_buffer[4]); // Z轴

    // 解析陀螺仪数据 (假设为小端序)
    raw_data.gyro_raw[0] = combineBytes(gyro_buffer[1], gyro_buffer[0]); // X轴
    raw_data.gyro_raw[1] = combineBytes(gyro_buffer[3], gyro_buffer[2]); // Y轴
    raw_data.gyro_raw[2] = combineBytes(gyro_buffer[5], gyro_buffer[4]); // Z轴

    // 解析温度数据
    raw_data.temp_raw = combineBytes(temp_buffer[1], temp_buffer[0]);

    raw_data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

bool SC7I22_Adapter::isConnected() {
    uint8_t device_id = getDeviceID();
    return (device_id == SC7I22_EXPECTED_ID);
}

IMU_Type SC7I22_Adapter::getType() const {
    return IMU_Type::SC7I22;
}

uint8_t SC7I22_Adapter::getDeviceID() {
    return readRegister(SC7I22_WHO_AM_I);
}

IMU_Status SC7I22_Adapter::reset() {
    return softReset();
}

IMU_Status SC7I22_Adapter::calibrate(uint32_t calibration_time) {
    if (!initialized) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "SC7I22 not initialized");
        return last_status;
    }

    Serial.println("开始SC7I22校准，请保持传感器静止...");
    
    // 清零偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    
    const int num_samples = calibration_time / 10; // 每10ms采样一次
    float accel_sum[3] = {0, 0, 0};
    float gyro_sum[3] = {0, 0, 0};
    int valid_samples = 0;
    
    uint32_t start_time = millis();
    
    for (int i = 0; i < num_samples; i++) {
        IMU_Data data;
        if (readData(data) == IMU_Status::OK) {
            accel_sum[0] += data.accel_x;
            accel_sum[1] += data.accel_y;
            accel_sum[2] += data.accel_z;
            
            gyro_sum[0] += data.gyro_x;
            gyro_sum[1] += data.gyro_y;
            gyro_sum[2] += data.gyro_z;
            
            valid_samples++;
        }
        
        delay(10);
        
        // 显示进度
        if (i % (num_samples / 10) == 0) {
            Serial.printf("校准进度: %d%%\n", (i * 100) / num_samples);
        }
    }
    
    if (valid_samples < num_samples / 2) {
        last_status = IMU_Status::ERROR_CALIBRATION;
        strcpy(error_message, "Insufficient valid samples for calibration");
        return last_status;
    }
    
    // 计算偏移量
    gyro_offset[0] = gyro_sum[0] / valid_samples;
    gyro_offset[1] = gyro_sum[1] / valid_samples;
    gyro_offset[2] = gyro_sum[2] / valid_samples;
    
    // 加速度计Z轴应该接近1g，X和Y轴接近0
    accel_offset[0] = accel_sum[0] / valid_samples;
    accel_offset[1] = accel_sum[1] / valid_samples;
    accel_offset[2] = (accel_sum[2] / valid_samples) - 1.0f; // 减去重力加速度
    
    uint32_t elapsed_time = millis() - start_time;
    
    Serial.printf("SC7I22校准完成 (用时: %lu ms, 有效样本: %d)\n", elapsed_time, valid_samples);
    Serial.printf("陀螺仪偏移: X=%.3f, Y=%.3f, Z=%.3f (度/秒)\n", 
                 gyro_offset[0], gyro_offset[1], gyro_offset[2]);
    Serial.printf("加速度计偏移: X=%.3f, Y=%.3f, Z=%.3f (g)\n", 
                 accel_offset[0], accel_offset[1], accel_offset[2]);
    
    last_status = IMU_Status::OK;
    strcpy(error_message, "SC7I22 calibration completed successfully");
    return IMU_Status::OK;
}

IMU_Status SC7I22_Adapter::setConfig(const IMU_Config& config) {
    current_config = config;
    
    if (initialized) {
        // 重新配置传感器
        IMU_Status status = configureAccelerometer();
        if (status != IMU_Status::OK) return status;
        
        status = configureGyroscope();
        if (status != IMU_Status::OK) return status;
        
        updateScaleFactors();
    }
    
    return IMU_Status::OK;
}

IMU_Status SC7I22_Adapter::getConfig(IMU_Config& config) {
    config = current_config;
    return IMU_Status::OK;
}

const char* SC7I22_Adapter::getLastError() {
    return error_message;
}

const char* SC7I22_Adapter::getInfo() {
    static char info_buffer[256];
    snprintf(info_buffer, sizeof(info_buffer),
            "SC7I22 IMU传感器\n"
            "I2C地址: 0x%02X\n"
            "设备ID: 0x%02X\n"
            "加速度计量程: ±%dg\n"
            "陀螺仪量程: ±%d度/秒\n"
            "采样率: %dHz\n"
            "数字滤波器: %s (带宽: %dHz)",
            device_address,
            getDeviceID(),
            current_config.accel_range,
            current_config.gyro_range,
            current_config.sample_rate,
            current_config.enable_filter ? "启用" : "禁用",
            current_config.filter_bandwidth);
    
    return info_buffer;
}

// 私有方法实现
bool SC7I22_Adapter::writeRegister(uint8_t reg, uint8_t value) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    Wire.write(value);
    return (Wire.endTransmission() == 0);
}

uint8_t SC7I22_Adapter::readRegister(uint8_t reg) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0xFF; // 通信失败
    }

    Wire.requestFrom(device_address, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }

    return 0xFF; // 读取失败
}

bool SC7I22_Adapter::readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return false;
    }

    Wire.requestFrom(device_address, length);
    uint8_t count = 0;
    while (Wire.available() && count < length) {
        buffer[count++] = Wire.read();
    }

    return (count == length);
}

int16_t SC7I22_Adapter::combineBytes(uint8_t high, uint8_t low) {
    return (int16_t)((high << 8) | low);
}

IMU_Status SC7I22_Adapter::softReset() {
    // SC7I22软件复位实现 (需要根据实际规格调整)
    // 这里提供一个通用的复位序列
    if (!writeRegister(SC7I22_CTRL_REG1, 0x00)) { // 禁用所有功能
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to write reset command to SC7I22");
        return last_status;
    }

    delay(10);

    // 启用加速度计和陀螺仪
    if (!writeRegister(SC7I22_CTRL_REG1, 0x0F)) { // 启用XYZ轴
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to enable SC7I22 sensors");
        return last_status;
    }

    delay(10);
    return IMU_Status::OK;
}

IMU_Status SC7I22_Adapter::configureAccelerometer() {
    // 设置加速度计量程
    uint8_t accel_config = mapAccelRange(current_config.accel_range);

    if (!writeRegister(SC7I22_CTRL_REG4, accel_config)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure accelerometer range");
        return last_status;
    }

    // 配置加速度计采样率和滤波器 (需要根据实际规格调整)
    uint8_t ctrl_reg1 = 0x0F; // 启用XYZ轴

    // 根据采样率设置ODR
    if (current_config.sample_rate >= 1000) {
        ctrl_reg1 |= 0x90; // 高采样率
    } else if (current_config.sample_rate >= 400) {
        ctrl_reg1 |= 0x70; // 中等采样率
    } else {
        ctrl_reg1 |= 0x50; // 低采样率
    }

    if (!writeRegister(SC7I22_CTRL_REG1, ctrl_reg1)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure accelerometer");
        return last_status;
    }

    return IMU_Status::OK;
}

IMU_Status SC7I22_Adapter::configureGyroscope() {
    // 设置陀螺仪量程
    uint8_t gyro_config = mapGyroRange(current_config.gyro_range);

    if (!writeRegister(SC7I22_CTRL_REG4, gyro_config)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure gyroscope range");
        return last_status;
    }

    // 配置陀螺仪采样率和滤波器 (需要根据实际规格调整)
    uint8_t ctrl_reg1 = 0x0F; // 启用XYZ轴

    // 根据采样率设置ODR
    if (current_config.sample_rate >= 1000) {
        ctrl_reg1 |= 0x90; // 高采样率
    } else if (current_config.sample_rate >= 400) {
        ctrl_reg1 |= 0x70; // 中等采样率
    } else {
        ctrl_reg1 |= 0x50; // 低采样率
    }

    if (!writeRegister(SC7I22_CTRL_REG1, ctrl_reg1)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure gyroscope");
        return last_status;
    }

    return IMU_Status::OK;
}

void SC7I22_Adapter::updateScaleFactors() {
    // 根据配置的量程更新转换因子 (需要根据实际规格调整)
    switch (current_config.accel_range) {
        case 2:  accel_scale = 2.0f / 32768.0f; break;   // ±2g
        case 4:  accel_scale = 4.0f / 32768.0f; break;   // ±4g
        case 8:  accel_scale = 8.0f / 32768.0f; break;   // ±8g
        case 16: accel_scale = 16.0f / 32768.0f; break;  // ±16g
        default: accel_scale = 16.0f / 32768.0f; break;  // 默认±16g
    }

    switch (current_config.gyro_range) {
        case 250:  gyro_scale = 250.0f / 32768.0f; break;   // ±250 dps
        case 500:  gyro_scale = 500.0f / 32768.0f; break;   // ±500 dps
        case 1000: gyro_scale = 1000.0f / 32768.0f; break;  // ±1000 dps
        case 2000: gyro_scale = 2000.0f / 32768.0f; break;  // ±2000 dps
        default:   gyro_scale = 2000.0f / 32768.0f; break;  // 默认±2000 dps
    }
}

uint8_t SC7I22_Adapter::mapAccelRange(uint16_t range) {
    switch (range) {
        case 2:  return SC7I22_ACCEL_FS_2G;
        case 4:  return SC7I22_ACCEL_FS_4G;
        case 8:  return SC7I22_ACCEL_FS_8G;
        case 16: return SC7I22_ACCEL_FS_16G;
        default: return SC7I22_ACCEL_FS_16G; // 默认±16g
    }
}

uint8_t SC7I22_Adapter::mapGyroRange(uint16_t range) {
    switch (range) {
        case 250:  return SC7I22_GYRO_FS_250;
        case 500:  return SC7I22_GYRO_FS_500;
        case 1000: return SC7I22_GYRO_FS_1000;
        case 2000: return SC7I22_GYRO_FS_2000;
        default:   return SC7I22_GYRO_FS_2000; // 默认±2000 dps
    }
}
