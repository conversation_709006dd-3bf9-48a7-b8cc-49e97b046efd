# 添加新传感器支持指南

本指南说明如何基于现有的传感器文件（SH5001_Sensor和WS_QMI8658）为IMU Fusion Library添加新的传感器支持。

## 设计理念

本项目采用基于现有传感器文件的扩展方式，而不是创建新的适配器文件。这样可以：

1. **保持代码简洁** - 避免创建过多的适配器文件
2. **利用现有代码** - 基于已经测试过的传感器实现
3. **易于维护** - 减少代码重复和维护负担
4. **向后兼容** - 保持与现有代码的兼容性

## 当前支持的传感器

### 1. SH5001传感器
- **文件**: `SH5001_Sensor.cpp` / `SH5001_Sensor.h`
- **I2C地址**: 0x37 (主) / 0x36 (备用)
- **设备ID**: 0xA1
- **特性**: 6轴IMU，内置温度传感器

### 2. QMI8658传感器
- **文件**: `WS_QMI8658.cpp` / `WS_QMI8658.h`
- **I2C地址**: 0x6B (主) / 0x6A (备用)
- **设备ID**: 0x05
- **特性**: 6轴IMU，高精度，需要SensorQMI8658库

## 添加新传感器的方法

### 方法1: 基于现有传感器文件扩展

如果您的新传感器与现有传感器类似，可以修改现有文件来支持：

#### 步骤1: 修改传感器类
在现有的传感器类中添加对新传感器的支持：

```cpp
// 在SH5001_Sensor.h中添加新的地址定义
#define NEW_SENSOR_ADDR     0x68
#define NEW_SENSOR_ID       0x70

// 在SH5001_Sensor.cpp中修改isConnected()方法
bool SH5001_Sensor::isConnected() {
    uint8_t device_id = getDeviceID();
    
    // 检查SH5001
    if (device_id == 0xA1) {
        return true;
    }
    
    // 检查新传感器
    if (device_id == NEW_SENSOR_ID) {
        return true;
    }
    
    return false;
}
```

#### 步骤2: 更新检测逻辑
在`IMU_Fusion_Library.cpp`的`autoDetectAndInit`方法中添加新传感器的检测：

```cpp
// 尝试检测新传感器
Serial.println("检测NewSensor传感器...");
SH5001_Sensor* new_sensor = new SH5001_Sensor(NEW_SENSOR_ADDR);
if (new_sensor->isConnected()) {
    sensor = new_sensor;
    sensor_owned = true;
    Serial.printf("检测到NewSensor传感器 (地址: 0x%02X)\n", NEW_SENSOR_ADDR);
    return IMU_Status::OK;
}
delete new_sensor;
```

### 方法2: 创建新的传感器文件

如果新传感器差异较大，可以创建新的传感器文件：

#### 步骤1: 创建传感器文件
创建 `NewSensor_Sensor.h` 和 `NewSensor_Sensor.cpp`，参考SH5001_Sensor的结构：

```cpp
// NewSensor_Sensor.h
#ifndef NEWSENSOR_SENSOR_H
#define NEWSENSOR_SENSOR_H

#include "IMU_Sensor_Interface.h"
#include <Wire.h>

#define NEWSENSOR_ADDR      0x68
#define NEWSENSOR_ID        0x70

class NewSensor_Sensor : public IMU_Sensor_Interface {
private:
    uint8_t device_address;
    bool initialized;
    
public:
    NewSensor_Sensor(uint8_t address = NEWSENSOR_ADDR);
    virtual ~NewSensor_Sensor();
    
    // 实现所有虚函数
    virtual IMU_Status begin(const IMU_Config& config = {}) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status readRawData(IMU_RawData& raw_data) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() const override;
    virtual uint8_t getDeviceID() override;
    virtual IMU_Status reset() override;
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) override;
    virtual IMU_Status setConfig(const IMU_Config& config) override;
    virtual IMU_Status getConfig(IMU_Config& config) override;
    virtual const char* getLastError() override;
    virtual const char* getInfo() override;
};

#endif
```

#### 步骤2: 实现传感器类
在 `NewSensor_Sensor.cpp` 中实现所有方法，参考SH5001_Sensor的实现。

#### 步骤3: 更新主库文件
在 `IMU_Fusion_Library.h` 中包含新的头文件：

```cpp
#include "NewSensor_Sensor.h"
```

在 `IMU_Fusion_Library.cpp` 中添加检测逻辑。

### 方法3: 使用配置文件方式

创建传感器配置文件，通过配置来支持新传感器：

#### 步骤1: 创建配置文件
创建 `sensor_configs.h`：

```cpp
// sensor_configs.h
#ifndef SENSOR_CONFIGS_H
#define SENSOR_CONFIGS_H

struct SensorConfig {
    const char* name;
    uint8_t i2c_address;
    uint8_t alt_address;
    uint8_t device_id_reg;
    uint8_t expected_id;
    uint16_t default_accel_range;
    uint16_t default_gyro_range;
};

const SensorConfig sensor_configs[] = {
    {"SH5001", 0x37, 0x36, 0x1F, 0xA1, 16, 2000},
    {"QMI8658", 0x6B, 0x6A, 0x00, 0x05, 4, 512},
    {"MPU6500", 0x68, 0x69, 0x75, 0x70, 8, 1000},
    {"MPU9250", 0x68, 0x69, 0x75, 0x71, 4, 500},
    // 添加新传感器配置
};

#endif
```

#### 步骤2: 实现通用检测函数
创建基于配置的通用检测和初始化函数。

## 实际示例：添加MPU6500支持

### 1. 修改传感器枚举
在 `IMU_Sensor_Interface.h` 中添加：

```cpp
enum class IMU_Type {
    UNKNOWN = 0,
    SH5001,
    QMI8658,
    MPU6500,    // 新增
    // ...
};
```

### 2. 创建MPU6500传感器文件
基于SH5001_Sensor创建MPU6500_Sensor文件，修改寄存器地址和通信协议。

### 3. 更新检测逻辑
在autoDetectAndInit中添加MPU6500的检测。

## 测试新传感器

### 1. 使用通用示例测试
使用 `Universal_Sensor_Example.ino` 测试新传感器：

```cpp
// 在detectSensor()函数中添加新传感器的检测逻辑
if (testI2CAddress(NEW_SENSOR_ADDR)) {
    uint8_t device_id = readI2CRegister(NEW_SENSOR_ADDR, NEW_SENSOR_ID_REG);
    if (device_id == NEW_SENSOR_EXPECTED_ID) {
        Serial.printf("发现NewSensor传感器 (地址: 0x%02X, ID: 0x%02X)\n", 
                     NEW_SENSOR_ADDR, device_id);
        return SENSOR_NEW_SENSOR;
    }
}
```

### 2. 验证功能
- 检查传感器检测是否正常
- 验证数据读取是否正确
- 测试校准功能
- 确认融合算法输出

## 最佳实践

1. **保持简洁** - 尽量复用现有代码，避免重复实现
2. **向后兼容** - 确保新增功能不影响现有传感器
3. **充分测试** - 在添加新传感器后进行完整测试
4. **文档更新** - 更新相关文档和示例
5. **错误处理** - 提供详细的错误信息和调试信息

## 常见问题

**Q: 如何确定新传感器的寄存器地址？**
A: 查看传感器的数据手册，找到设备ID寄存器和数据寄存器的地址。

**Q: 如何处理不同的数据格式？**
A: 在readData()和readRawData()方法中实现相应的数据转换逻辑。

**Q: 如何添加传感器特有的功能？**
A: 可以在传感器类中添加公共方法，或者通过配置参数来支持。

**Q: 如何优化多传感器检测的性能？**
A: 可以根据实际需要调整检测顺序，优先检测最常用的传感器。

## 总结

通过以上方法，您可以基于现有的传感器文件轻松添加新的传感器支持，而无需创建复杂的适配器架构。这种方式既保持了代码的简洁性，又提供了足够的灵活性来支持各种不同的IMU传感器。
