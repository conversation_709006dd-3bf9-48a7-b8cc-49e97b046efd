/**
 * @file IMU_Fusion_Library.cpp
 * @brief IMU融合库主实现文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 */

#include "IMU_Fusion_Library.h"
#include <Wire.h>
#include <string.h>

IMU_Fusion_Library::IMU_Fusion_Library() 
    : sensor(nullptr)
    , fusion_engine(nullptr)
    , sensor_owned(false)
    , engine_owned(false)
    , initialized(false)
    , last_update_time(0)
    , update_interval(10) // 默认100Hz
    , total_samples(0)
    , failed_samples(0)
    , start_time(0)
{
    memset(info_buffer, 0, sizeof(info_buffer));
}

IMU_Fusion_Library::IMU_Fusion_Library(IMU_Sensor_Interface* sensor_ptr, bool take_ownership)
    : sensor(sensor_ptr)
    , fusion_engine(nullptr)
    , sensor_owned(take_ownership)
    , engine_owned(false)
    , initialized(false)
    , last_update_time(0)
    , update_interval(10)
    , total_samples(0)
    , failed_samples(0)
    , start_time(0)
{
    memset(info_buffer, 0, sizeof(info_buffer));
}

IMU_Fusion_Library::~IMU_Fusion_Library() {
    if (sensor_owned && sensor) {
        delete sensor;
    }
    if (engine_owned && fusion_engine) {
        delete fusion_engine;
    }
}

IMU_Status IMU_Fusion_Library::begin(const IMU_Config& sensor_config, 
                                     const Fusion_Config& fusion_config) {
    // 如果没有传感器，尝试自动检测
    if (!sensor) {
        IMU_Status status = autoDetectAndInit();
        if (status != IMU_Status::OK) {
            return status;
        }
    }

    // 初始化传感器
    IMU_Status status = sensor->begin(sensor_config);
    if (status != IMU_Status::OK) {
        return status;
    }

    // 创建融合引擎
    if (!fusion_engine) {
        fusion_engine = new IMU_Fusion_Engine();
        engine_owned = true;
    }

    // 初始化融合引擎
    Fusion_Status fusion_status = fusion_engine->begin(fusion_config);
    if (fusion_status != Fusion_Status::OK) {
        return IMU_Status::ERROR_INIT;
    }

    initialized = true;
    start_time = millis();
    last_update_time = start_time;
    total_samples = 0;
    failed_samples = 0;

    return IMU_Status::OK;
}

IMU_Status IMU_Fusion_Library::autoDetectAndInit(int sda_pin, int scl_pin, uint32_t i2c_freq) {
    // 初始化I2C
    Wire.begin(sda_pin, scl_pin);
    Wire.setClock(i2c_freq);

    // 尝试检测SH5001传感器
    SH5001_Sensor* sh5001 = new SH5001_Sensor(SH5001_ADDR);
    if (sh5001->isConnected()) {
        sensor = sh5001;
        sensor_owned = true;
        Serial.println("检测到SH5001传感器 (地址: 0x37)");
        return IMU_Status::OK;
    }
    delete sh5001;

    // 尝试备用地址
    sh5001 = new SH5001_Sensor(SH5001_ADDR_ALT);
    if (sh5001->isConnected()) {
        sensor = sh5001;
        sensor_owned = true;
        Serial.println("检测到SH5001传感器 (地址: 0x36)");
        return IMU_Status::OK;
    }
    delete sh5001;

    // 未检测到支持的传感器
    Serial.println("未检测到支持的IMU传感器");
    return IMU_Status::ERROR_COMMUNICATION;
}

IMU_Status IMU_Fusion_Library::update() {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    uint32_t current_time = millis();
    if (current_time - last_update_time < update_interval) {
        return IMU_Status::OK; // 还未到更新时间
    }

    // 读取传感器数据
    IMU_Data data;
    IMU_Status status = sensor->readData(data);
    if (status != IMU_Status::OK) {
        failed_samples++;
        return status;
    }

    // 更新融合算法
    Fusion_Status fusion_status = fusion_engine->update(data);
    if (fusion_status != Fusion_Status::OK) {
        failed_samples++;
        return IMU_Status::ERROR_SENSOR_FAILURE;
    }

    total_samples++;
    last_update_time = current_time;
    return IMU_Status::OK;
}

IMU_Status IMU_Fusion_Library::updateManual(float accel_x, float accel_y, float accel_z,
                                            float gyro_x, float gyro_y, float gyro_z) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    Fusion_Status fusion_status = fusion_engine->update(accel_x, accel_y, accel_z,
                                                        gyro_x, gyro_y, gyro_z);
    if (fusion_status != Fusion_Status::OK) {
        failed_samples++;
        return IMU_Status::ERROR_SENSOR_FAILURE;
    }

    total_samples++;
    last_update_time = millis();
    return IMU_Status::OK;
}

IMU_Status IMU_Fusion_Library::getQuaternion(float& w, float& x, float& y, float& z) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    Quaternion_Data quat;
    Fusion_Status status = fusion_engine->getQuaternion(quat);
    if (status == Fusion_Status::OK) {
        w = quat.w;
        x = quat.x;
        y = quat.y;
        z = quat.z;
        return IMU_Status::OK;
    }
    return IMU_Status::ERROR_INVALID_DATA;
}

IMU_Status IMU_Fusion_Library::getQuaternion(Quaternion_Data& quat) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    Fusion_Status status = fusion_engine->getQuaternion(quat);
    return (status == Fusion_Status::OK) ? IMU_Status::OK : IMU_Status::ERROR_INVALID_DATA;
}

IMU_Status IMU_Fusion_Library::getEulerAngles(float& roll, float& pitch, float& yaw) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    Fusion_Status status = fusion_engine->getEulerAngles(roll, pitch, yaw);
    return (status == Fusion_Status::OK) ? IMU_Status::OK : IMU_Status::ERROR_INVALID_DATA;
}

IMU_Status IMU_Fusion_Library::getAttitude(Attitude_Data& attitude) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }

    Fusion_Status status = fusion_engine->getAttitude(attitude);
    return (status == Fusion_Status::OK) ? IMU_Status::OK : IMU_Status::ERROR_INVALID_DATA;
}

float IMU_Fusion_Library::getHeading() {
    if (!initialized) {
        return 0.0f;
    }
    return fusion_engine->getHeading();
}

IMU_Status IMU_Fusion_Library::getRawData(IMU_Data& data) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }
    return sensor->readData(data);
}

IMU_Status IMU_Fusion_Library::calibrate(uint32_t calibration_time) {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }
    return sensor->calibrate(calibration_time);
}

IMU_Status IMU_Fusion_Library::resetFusion() {
    if (!initialized) {
        return IMU_Status::ERROR_INIT;
    }
    
    Fusion_Status status = fusion_engine->reset();
    return (status == Fusion_Status::OK) ? IMU_Status::OK : IMU_Status::ERROR_INIT;
}

IMU_Status IMU_Fusion_Library::setUpdateFrequency(float frequency) {
    if (frequency <= 0) {
        return IMU_Status::ERROR_INVALID_DATA;
    }
    
    update_interval = (uint32_t)(1000.0f / frequency);
    return IMU_Status::OK;
}

IMU_Type IMU_Fusion_Library::getSensorType() {
    if (!sensor) {
        return IMU_Type::UNKNOWN;
    }
    return sensor->getType();
}

const char* IMU_Fusion_Library::getSensorInfo() {
    if (!sensor) {
        return "No sensor connected";
    }
    return sensor->getInfo();
}

const char* IMU_Fusion_Library::getLibraryInfo() {
    snprintf(info_buffer, sizeof(info_buffer),
             "IMU Fusion Library v%s\n"
             "Author: %s\n"
             "Sensor: %s\n"
             "Status: %s\n"
             "Samples: %lu (Failed: %lu)\n"
             "Uptime: %lu ms",
             IMU_FUSION_VERSION,
             IMU_FUSION_AUTHOR,
             sensor ? sensor->getInfo() : "None",
             initialized ? "Initialized" : "Not initialized",
             total_samples,
             failed_samples,
             millis() - start_time);
    return info_buffer;
}

void IMU_Fusion_Library::getStatistics(uint32_t& total, uint32_t& failed, 
                                       float& success_rate, uint32_t& uptime) {
    total = total_samples;
    failed = failed_samples;
    success_rate = (total_samples > 0) ? ((float)(total_samples - failed_samples) / total_samples * 100.0f) : 0.0f;
    uptime = millis() - start_time;
}

void IMU_Fusion_Library::clearStatistics() {
    total_samples = 0;
    failed_samples = 0;
    start_time = millis();
}

bool IMU_Fusion_Library::isInitialized() const {
    return initialized;
}

bool IMU_Fusion_Library::isSensorConnected() {
    if (!sensor) {
        return false;
    }
    return sensor->isConnected();
}

const char* IMU_Fusion_Library::getLastError() {
    if (sensor) {
        return sensor->getLastError();
    }
    return "No sensor available";
}

void IMU_Fusion_Library::printQuaternion(int precision) {
    if (!initialized) {
        Serial.println("Library not initialized");
        return;
    }

    Quaternion_Data quat;
    if (getQuaternion(quat) == IMU_Status::OK) {
        Serial.print(quat.w, precision);
        Serial.print(",");
        Serial.print(quat.x, precision);
        Serial.print(",");
        Serial.print(quat.y, precision);
        Serial.print(",");
        Serial.print(quat.z, precision);
        Serial.println();
    }
}

void IMU_Fusion_Library::printEulerAngles(int precision) {
    if (!initialized) {
        Serial.println("Library not initialized");
        return;
    }

    float roll, pitch, yaw;
    if (getEulerAngles(roll, pitch, yaw) == IMU_Status::OK) {
        Serial.print("Roll: ");
        Serial.print(roll, precision);
        Serial.print("°, Pitch: ");
        Serial.print(pitch, precision);
        Serial.print("°, Yaw: ");
        Serial.print(yaw, precision);
        Serial.println("°");
    }
}

void IMU_Fusion_Library::printSensorData(int precision) {
    if (!initialized) {
        Serial.println("Library not initialized");
        return;
    }

    IMU_Data data;
    if (getRawData(data) == IMU_Status::OK) {
        Serial.print("Accel: X=");
        Serial.print(data.accel_x, precision);
        Serial.print("g, Y=");
        Serial.print(data.accel_y, precision);
        Serial.print("g, Z=");
        Serial.print(data.accel_z, precision);
        Serial.print("g | Gyro: X=");
        Serial.print(data.gyro_x, precision);
        Serial.print("°/s, Y=");
        Serial.print(data.gyro_y, precision);
        Serial.print("°/s, Z=");
        Serial.print(data.gyro_z, precision);
        Serial.print("°/s | Temp: ");
        Serial.print(data.temperature, 1);
        Serial.println("°C");
    }
}

IMU_Status IMU_Fusion_Library::setSensor(IMU_Sensor_Interface* sensor_ptr, bool take_ownership) {
    if (sensor_owned && sensor) {
        delete sensor;
    }

    sensor = sensor_ptr;
    sensor_owned = take_ownership;
    initialized = false; // 需要重新初始化

    return IMU_Status::OK;
}

IMU_Sensor_Interface* IMU_Fusion_Library::getSensor() {
    return sensor;
}

IMU_Fusion_Engine* IMU_Fusion_Library::getFusionEngine() {
    return fusion_engine;
}

// 便利函数实现
Fusion_Config createDefaultFusionConfig(float sample_rate) {
    Fusion_Config config = {};
    config.convention = FusionConventionNwu;
    config.gain = 0.5f;
    config.gyroscope_range = 2000.0f;
    config.acceleration_rejection = 10.0f;
    config.magnetic_rejection = 10.0f;
    config.recovery_trigger_period = (uint32_t)(5.0f * sample_rate); // 5秒
    config.sample_period = 1.0f / sample_rate;
    config.use_magnetometer = false;
    return config;
}

IMU_Config createDefaultSensorConfig(uint16_t accel_range, uint16_t gyro_range, uint16_t sample_rate) {
    IMU_Config config = {};
    config.accel_range = accel_range;
    config.gyro_range = gyro_range;
    config.sample_rate = sample_rate;
    config.enable_filter = true;
    config.filter_bandwidth = 40;
    return config;
}
