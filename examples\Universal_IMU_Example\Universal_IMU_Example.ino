/**
 * @file Universal_IMU_Example.ino
 * @brief 通用IMU接口使用示例
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这个示例展示了如何使用通用IMU接口支持多种传感器
 * 支持自动检测SH5001、QMI8658、MPU6500、MPU9250、SC7I22等传感器
 * 并通过fusion库输出优化后的四元数
 */

#include <IMU_Fusion_Library.h>

// 创建IMU融合库实例
IMU_Fusion_Library imu_fusion;

// 配置参数
#define SAMPLE_RATE_HZ      100     // 采样率 100Hz
#define UPDATE_INTERVAL_MS  10      // 更新间隔 10ms
#define PRINT_INTERVAL_MS   100     // 打印间隔 100ms (10Hz输出)

// I2C引脚定义 (ESP32-C3)
#define SDA_PIN 8
#define SCL_PIN 9

// 时间控制变量
unsigned long last_update_time = 0;
unsigned long last_print_time = 0;
unsigned long last_stats_time = 0;

// 统计变量
uint32_t loop_count = 0;
uint32_t successful_updates = 0;

void setup() {
    // 初始化串口
    Serial.begin(115200);
    while (!Serial) {
        delay(10);
    }
    
    Serial.println("========================================");
    Serial.println("通用IMU接口使用示例");
    Serial.println("支持多种传感器自动检测");
    Serial.println("========================================");
    
    // 创建传感器和融合算法配置
    IMU_Config sensor_config = createDefaultSensorConfig(16, 2000, 125);
    Fusion_Config fusion_config = createDefaultFusionConfig(SAMPLE_RATE_HZ);
    
    // 显示配置信息
    Serial.println("传感器配置:");
    Serial.printf("  加速度计量程: ±%dg\n", sensor_config.accel_range);
    Serial.printf("  陀螺仪量程: ±%d度/秒\n", sensor_config.gyro_range);
    Serial.printf("  采样率: %dHz\n", sensor_config.sample_rate);
    Serial.printf("  数字滤波器: %s\n", sensor_config.enable_filter ? "启用" : "禁用");
    Serial.println();
    
    Serial.println("融合算法配置:");
    Serial.printf("  采样周期: %.3f秒\n", fusion_config.sample_period);
    Serial.printf("  融合增益: %.2f\n", fusion_config.gain);
    Serial.printf("  使用磁力计: %s\n", fusion_config.use_magnetometer ? "是" : "否");
    Serial.println();
    
    // 自动检测并初始化IMU传感器
    Serial.println("正在自动检测IMU传感器...");
    IMU_Status status = imu_fusion.autoDetectAndInit(SDA_PIN, SCL_PIN, 400000);
    
    if (status != IMU_Status::OK) {
        Serial.println("错误: 传感器检测失败!");
        Serial.println("支持的传感器类型:");
        Serial.println("- SH5001 (地址: 0x37/0x36)");
        Serial.println("- QMI8658 (地址: 0x6B/0x6A)");
        Serial.println("- MPU6500 (地址: 0x68/0x69)");
        Serial.println("- MPU9250 (地址: 0x68/0x69)");
        Serial.println("- SC7I22 (地址: 0x6A/0x6B)");
        Serial.println();
        Serial.println("请检查硬件连接和供电");
        while (1) {
            delay(1000);
        }
    }
    
    // 初始化融合库
    Serial.println("正在初始化融合算法...");
    status = imu_fusion.begin(sensor_config, fusion_config);
    
    if (status != IMU_Status::OK) {
        Serial.println("错误: 融合库初始化失败!");
        Serial.printf("错误信息: %s\n", imu_fusion.getLastError());
        while (1) {
            delay(1000);
        }
    }
    
    // 显示传感器信息
    Serial.println("传感器信息:");
    Serial.println(imu_fusion.getSensorInfo());
    Serial.println();
    
    // 显示库信息
    Serial.println("库信息:");
    Serial.println(imu_fusion.getLibraryInfo());
    Serial.println();
    
    // 设置更新频率
    imu_fusion.setUpdateFrequency(SAMPLE_RATE_HZ);
    
    // 询问是否进行校准
    Serial.println("是否进行传感器校准? (y/n)");
    Serial.println("校准期间请保持传感器静止...");
    
    // 等待用户输入 (简化版本，实际应用中可以跳过)
    delay(3000); // 给用户3秒时间考虑
    
    Serial.println("开始校准...");
    status = imu_fusion.calibrate(5000); // 5秒校准
    
    if (status == IMU_Status::OK) {
        Serial.println("校准完成!");
    } else {
        Serial.println("校准失败，使用默认参数");
    }
    
    Serial.println("========================================");
    Serial.println("系统准备就绪，开始数据采集...");
    Serial.println("输出格式: w,x,y,z (四元数)");
    Serial.println("========================================");
    
    last_update_time = millis();
    last_print_time = millis();
    last_stats_time = millis();
}

void loop() {
    unsigned long current_time = millis();
    loop_count++;
    
    // 控制更新频率
    if (current_time - last_update_time >= UPDATE_INTERVAL_MS) {
        last_update_time = current_time;
        
        // 更新IMU数据并执行融合
        IMU_Status status = imu_fusion.update();
        
        if (status == IMU_Status::OK) {
            successful_updates++;
        } else {
            // 只在连续失败时打印错误信息，避免刷屏
            static uint32_t last_error_time = 0;
            if (current_time - last_error_time > 1000) { // 每秒最多打印一次错误
                Serial.printf("警告: 数据更新失败 - %s\n", imu_fusion.getLastError());
                last_error_time = current_time;
            }
        }
    }
    
    // 控制输出频率
    if (current_time - last_print_time >= PRINT_INTERVAL_MS) {
        last_print_time = current_time;
        
        // 输出四元数 (与原有格式兼容)
        imu_fusion.printQuaternion(4);
    }
    
    // 每10秒显示一次统计信息
    if (current_time - last_stats_time >= 10000) {
        last_stats_time = current_time;
        
        uint32_t total_samples, failed_samples, uptime;
        float success_rate;
        imu_fusion.getStatistics(total_samples, failed_samples, success_rate, uptime);
        
        Serial.println();
        Serial.println("========== 统计信息 ==========");
        Serial.printf("运行时间: %lu 秒\n", uptime / 1000);
        Serial.printf("总采样数: %lu\n", total_samples);
        Serial.printf("失败采样数: %lu\n", failed_samples);
        Serial.printf("成功率: %.1f%%\n", success_rate);
        Serial.printf("循环频率: %.1f Hz\n", loop_count * 1000.0f / uptime);
        Serial.printf("更新频率: %.1f Hz\n", successful_updates * 1000.0f / uptime);
        Serial.println("==============================");
        Serial.println();
    }
    
    // 短暂延时，避免占用过多CPU
    delay(1);
}

/**
 * @brief 打印传感器原始数据 (可选功能)
 */
void printRawSensorData() {
    IMU_Data raw_data;
    IMU_Status status = imu_fusion.getRawData(raw_data);
    
    if (status == IMU_Status::OK) {
        Serial.printf("Raw: A[%.3f,%.3f,%.3f] G[%.1f,%.1f,%.1f] T[%.1f]\n",
                     raw_data.accel_x, raw_data.accel_y, raw_data.accel_z,
                     raw_data.gyro_x, raw_data.gyro_y, raw_data.gyro_z,
                     raw_data.temperature);
    }
}

/**
 * @brief 打印欧拉角 (可选功能)
 */
void printEulerAngles() {
    float roll, pitch, yaw;
    IMU_Status status = imu_fusion.getEulerAngles(roll, pitch, yaw);
    
    if (status == IMU_Status::OK) {
        Serial.printf("Euler: Roll=%.1f° Pitch=%.1f° Yaw=%.1f°\n", roll, pitch, yaw);
    }
}

/**
 * @brief 检查传感器连接状态
 */
void checkSensorConnection() {
    if (!imu_fusion.isSensorConnected()) {
        Serial.println("警告: 传感器连接丢失!");
        // 可以在这里添加重连逻辑
    }
}
