/**
 * @file Sensor_Configuration_Example.ino
 * @brief 传感器配置示例
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这个示例展示了如何为不同的传感器配置不同的参数
 * 以及如何手动选择和配置特定的传感器
 */

#include <IMU_Fusion_Library.h>

// 创建IMU融合库实例
IMU_Fusion_Library imu_fusion;

// I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

void setup() {
    Serial.begin(115200);
    while (!Serial) {
        delay(10);
    }
    
    Serial.println("========================================");
    Serial.println("传感器配置示例");
    Serial.println("========================================");
    
    // 演示传感器扫描
    demonstrateSensorScanning();
    
    // 演示手动传感器配置
    demonstrateManualConfiguration();
    
    // 演示不同传感器的最佳配置
    demonstrateOptimalConfigurations();
}

void loop() {
    // 主循环中进行正常的数据采集
    static unsigned long last_update = 0;
    
    if (millis() - last_update >= 100) { // 10Hz输出
        last_update = millis();
        
        if (imu_fusion.isInitialized()) {
            imu_fusion.update();
            imu_fusion.printQuaternion(4);
        }
    }
    
    delay(1);
}

/**
 * @brief 演示传感器扫描功能
 */
void demonstrateSensorScanning() {
    Serial.println("1. 传感器扫描演示");
    Serial.println("-------------------");
    
    IMU_Sensor_Factory factory;
    
    // 扫描所有支持的传感器
    Sensor_Detection_Result results[10];
    size_t found_count = factory.scanAllSensors(results, 10, SDA_PIN, SCL_PIN, 400000);
    
    Serial.printf("扫描完成，发现 %zu 个传感器:\n", found_count);
    
    if (found_count > 0) {
        IMU_Sensor_Factory::printScanResults(results, found_count);
        
        // 显示每个传感器的详细信息
        for (size_t i = 0; i < found_count; i++) {
            Serial.printf("\n传感器 %zu 详细信息:\n", i + 1);
            
            IMU_Sensor_Interface* sensor = factory.createSensor(
                results[i].type, results[i].i2c_address, SDA_PIN, SCL_PIN);
            
            if (sensor) {
                // 初始化传感器以获取详细信息
                IMU_Config config = createDefaultSensorConfig();
                if (sensor->begin(config) == IMU_Status::OK) {
                    Serial.println(sensor->getInfo());
                } else {
                    Serial.printf("初始化失败: %s\n", sensor->getLastError());
                }
                delete sensor;
            }
        }
    } else {
        Serial.println("未发现任何传感器");
    }
    
    Serial.println("\n========================================\n");
}

/**
 * @brief 演示手动传感器配置
 */
void demonstrateManualConfiguration() {
    Serial.println("2. 手动传感器配置演示");
    Serial.println("---------------------");
    
    IMU_Sensor_Factory factory;
    
    // 尝试手动创建不同类型的传感器
    IMU_Type sensor_types[] = {
        IMU_Type::SH5001,
        IMU_Type::QMI8658,
        IMU_Type::MPU6500,
        IMU_Type::MPU9250,
        IMU_Type::SC7I22
    };
    
    for (size_t i = 0; i < sizeof(sensor_types) / sizeof(sensor_types[0]); i++) {
        IMU_Type type = sensor_types[i];
        const char* name = IMU_Sensor_Factory::getSensorName(type);
        
        Serial.printf("尝试创建 %s 传感器...\n", name);
        
        IMU_Sensor_Interface* sensor = factory.createSensor(type, 0, SDA_PIN, SCL_PIN);
        
        if (sensor) {
            if (sensor->isConnected()) {
                Serial.printf("✓ %s 连接成功\n", name);
                
                // 演示不同的配置
                demonstrateConfigurationOptions(sensor, type);
                
                // 如果这是第一个成功的传感器，用它来初始化融合库
                if (!imu_fusion.isInitialized()) {
                    IMU_Config sensor_config = getOptimalConfigForSensor(type);
                    Fusion_Config fusion_config = createDefaultFusionConfig(100.0f);
                    
                    imu_fusion.setSensor(sensor, true); // 转移所有权
                    if (imu_fusion.begin(sensor_config, fusion_config) == IMU_Status::OK) {
                        Serial.printf("✓ 使用 %s 初始化融合库成功\n", name);
                    }
                    sensor = nullptr; // 所有权已转移
                }
            } else {
                Serial.printf("✗ %s 未连接\n", name);
            }
            
            if (sensor) {
                delete sensor;
            }
        } else {
            Serial.printf("✗ %s 创建失败: %s\n", name, factory.getLastError());
        }
        
        Serial.println();
    }
    
    Serial.println("========================================\n");
}

/**
 * @brief 演示配置选项
 */
void demonstrateConfigurationOptions(IMU_Sensor_Interface* sensor, IMU_Type type) {
    const char* name = IMU_Sensor_Factory::getSensorName(type);
    
    Serial.printf("  %s 配置选项:\n", name);
    
    // 测试不同的量程配置
    uint16_t accel_ranges[] = {2, 4, 8, 16};
    uint16_t gyro_ranges[] = {250, 500, 1000, 2000};
    
    Serial.print("  支持的加速度计量程: ");
    for (size_t i = 0; i < sizeof(accel_ranges) / sizeof(accel_ranges[0]); i++) {
        IMU_Config config = createDefaultSensorConfig();
        config.accel_range = accel_ranges[i];
        
        if (sensor->setConfig(config) == IMU_Status::OK) {
            Serial.printf("±%dg ", accel_ranges[i]);
        }
    }
    Serial.println();
    
    Serial.print("  支持的陀螺仪量程: ");
    for (size_t i = 0; i < sizeof(gyro_ranges) / sizeof(gyro_ranges[0]); i++) {
        IMU_Config config = createDefaultSensorConfig();
        config.gyro_range = gyro_ranges[i];
        
        if (sensor->setConfig(config) == IMU_Status::OK) {
            Serial.printf("±%d°/s ", gyro_ranges[i]);
        }
    }
    Serial.println();
    
    // 恢复默认配置
    IMU_Config default_config = createDefaultSensorConfig();
    sensor->setConfig(default_config);
}

/**
 * @brief 演示不同传感器的最佳配置
 */
void demonstrateOptimalConfigurations() {
    Serial.println("3. 不同传感器的推荐配置");
    Serial.println("-------------------------");
    
    IMU_Type sensor_types[] = {
        IMU_Type::SH5001,
        IMU_Type::QMI8658,
        IMU_Type::MPU6500,
        IMU_Type::MPU9250,
        IMU_Type::SC7I22
    };
    
    for (size_t i = 0; i < sizeof(sensor_types) / sizeof(sensor_types[0]); i++) {
        IMU_Type type = sensor_types[i];
        const char* name = IMU_Sensor_Factory::getSensorName(type);
        IMU_Config config = getOptimalConfigForSensor(type);
        
        Serial.printf("%s 推荐配置:\n", name);
        Serial.printf("  加速度计量程: ±%dg\n", config.accel_range);
        Serial.printf("  陀螺仪量程: ±%d°/s\n", config.gyro_range);
        Serial.printf("  采样率: %dHz\n", config.sample_rate);
        Serial.printf("  数字滤波器: %s", config.enable_filter ? "启用" : "禁用");
        if (config.enable_filter) {
            Serial.printf(" (带宽: %dHz)", config.filter_bandwidth);
        }
        Serial.println();
        Serial.println();
    }
    
    Serial.println("========================================\n");
}

/**
 * @brief 获取特定传感器的最佳配置
 */
IMU_Config getOptimalConfigForSensor(IMU_Type type) {
    IMU_Config config = createDefaultSensorConfig();
    
    switch (type) {
        case IMU_Type::SH5001:
            // SH5001 优化配置
            config.accel_range = 16;        // ±16g (高动态范围)
            config.gyro_range = 2000;       // ±2000°/s
            config.sample_rate = 125;       // 125Hz (平衡性能和功耗)
            config.enable_filter = true;
            config.filter_bandwidth = 40;   // 40Hz低通滤波
            break;
            
        case IMU_Type::QMI8658:
            // QMI8658 优化配置
            config.accel_range = 8;         // ±8g (中等动态范围)
            config.gyro_range = 1000;       // ±1000°/s
            config.sample_rate = 200;       // 200Hz (高采样率)
            config.enable_filter = true;
            config.filter_bandwidth = 50;   // 50Hz低通滤波
            break;
            
        case IMU_Type::MPU6500:
            // MPU6500 优化配置
            config.accel_range = 8;         // ±8g
            config.gyro_range = 1000;       // ±1000°/s
            config.sample_rate = 1000;      // 1000Hz (高性能)
            config.enable_filter = true;
            config.filter_bandwidth = 42;   // 42Hz低通滤波
            break;
            
        case IMU_Type::MPU9250:
            // MPU9250 优化配置 (9轴传感器)
            config.accel_range = 4;         // ±4g (精确测量)
            config.gyro_range = 500;        // ±500°/s
            config.sample_rate = 200;       // 200Hz
            config.enable_filter = true;
            config.filter_bandwidth = 20;   // 20Hz低通滤波 (更平滑)
            break;
            
        case IMU_Type::SC7I22:
            // SC7I22 优化配置
            config.accel_range = 16;        // ±16g
            config.gyro_range = 2000;       // ±2000°/s
            config.sample_rate = 1000;      // 1000Hz
            config.enable_filter = true;
            config.filter_bandwidth = 50;   // 50Hz低通滤波
            break;
            
        default:
            // 使用默认配置
            break;
    }
    
    return config;
}
